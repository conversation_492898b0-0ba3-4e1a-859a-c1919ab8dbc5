<svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 427319436" filter="url(#filter0_d_2002_5225)">
<rect x="12" y="8" width="57" height="57" rx="28.5" fill="url(#paint0_linear_2002_5225)" shape-rendering="crispEdges"/>
<path id="Vector" d="M36.7845 29.4992C36.4447 29.283 36 29.5271 36 29.9299V42.5369C36 42.9397 36.4447 43.1838 36.7845 42.9675L46.69 36.664C47.0052 36.4634 47.0052 36.0033 46.69 35.8027L36.7845 29.4992Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_2002_5225" x="0.8" y="0.8" width="79.4" height="79.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5.6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2002_5225"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2002_5225" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2002_5225" x1="40.5" y1="8" x2="32.6926" y2="88.2935" gradientUnits="userSpaceOnUse">
<stop stop-color="#54AD84"/>
<stop offset="1" stop-color="#6798D5"/>
</linearGradient>
</defs>
</svg>
