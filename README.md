This site is designed to showcase our services, values, and team, and to provide visitors with easy access to the information they need. It is built with a focus on performance, accessibility, and modern web standards.




## Running dev server: 
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `src/app/page.tsx`. The page auto-updates as you edit the file.

## Building: 
```bash
npm run build
```
This project uses next.js for static site generation(SSG). The rendered files (html, js, css) will be avilable at `/out`.


## Notes 
This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>ei<PERSON>](https://vercel.com/font), a new font family for Vercel.
