///// Types for HomePage //////
//////////////////////////////////////////////////

import { LucideIcon } from "lucide-react";
import { ReactNode } from "react";

// types for header
export type NavLink = {
    label: string;
    href: string;
};

export type ClientSideHeaderProps = {
    navLinks: NavLink[];
}

// types for heroSlider section
export type SlideDotsProps = {
    count: number;
    current: number;
    onClick: (index: number) => void;
};

// types for state section
export type StatItemProps = {
    endValue: number;
    title: string;
    description: string;
    suffix?: string;
}

// types for partner section 
export type CardProps = {
    children?: React.ReactNode;
    className?: string;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

// types for latest section
export type LatestCardProps = {
    image: string;
    title: string;
    date: string | Date;
    description: string;
    className?: string;
} & React.HTMLAttributes<HTMLDivElement>;


///// Types for aboutPage //////
//////////////////////////////////////////////////

export type PageHeroProps  = {
    backgroundImage: string;
    title?: string;
    subtitle?: string;
    breadcrumb?: {
        links: Array<{
            label: string;
            href: string;
        }>;
    };
    height?: string;      
}


///// Types for servicesDetailsPage //////
//////////////////////////////////////////////////

export type ServiceCardProps = {
    text: string;
}

export type ServiceData = {
    id: string;
    number: string;
    title: string;
    heading: string;
    description: string;
    imageUrl: string;
    imageAlt: string;
}        

export type ServicesSectionProps = {
    services: ServiceData[];
}

export type MemberCardProps = {
    imageUrl: string;
    name: string;
    title: string;
    className?: string;
    about?: string;
    achievements?: string;
    links?: {
        youtube?: string;
        linkedin?: string;
        bio?: string;
    };
}

export type MemberModalProps =  {
    isOpen: boolean;
    onClose: () => void;
    member: {
        imageUrl: string;
        name: string;
        title: string;
        about?: string;
        achievements?: string;
        links?: {
            youtube?: string;
            linkedin?: string;
            bio?: string;
        };
    };
}

export type AccordionProps = {
    title: string;
    children?: ReactNode;
    isOpen?: boolean;
    className?: string;
};

export type  GovernanceItem  = {
    title: string;
    content: string;
}

export type  CertificationCardProps =  {
    isoNumber: string;
    date: string;
    className?: string;
}


export type FeaturedStoryProp = {
    image: string;
    title: string;
    date: string;
    description: string;
};

// Should be removed as it will be found after project PR accepted

///// Types for projectPage //////
//////////////////////////////////////////////////

export type FilterOption =  {
    label: string;
    value: string;
    count: number;
}


///// Types for newsPage //////
//////////////////////////////////////////////////

export type News = {
    id: number;
    title: string;
    date: Date;
    type:string;
    description: string;
    subDescription: string;
    image: string | string[];
}

export type SliderProps = {
    images?: string[];
    interval?: number;
  }

export type Project = {
    id: number;
    name: string;
    sector: string;
    location: string;
    image: string | string[];
    duration: string;
    description: string
}

export type ProjectCardProps = {
    project: Project;
}

export type InfoCardProps = {
    icon: string;
    hoverIcon?: string;
    title: string;
    primaryText: string;
}

export type NextProjectSectionProps  = {
    title: string;
    service: string;
    subService?: string;
    imageUrl: string;
    projectUrl: string;
}

///// Types for equipment //////
//////////////////////////////////////////////////
export type FeatureCardProps = {
    name: string;
    description: string;
}

export type PerformanceHighlightsProp = {
    title: string;
    description: string;
    icon: string;
}

export type EquipmentProps  = {
    id:number,
    imageUrl: string | string[];
    text: string;
    label: string;
    PerformanceHighlights: PerformanceHighlightsProp[] ,
    relatedProjects: Project[]
}


export type EquipmentModalProps = {
    isOpen: boolean;
    onClose: () => void;
    equipment: {
        imageUrl: string[];
        text: string;
        label?: string;
        PerformanceHighlights: PerformanceHighlightsProp[];
        relatedProjects: Project[];
    };
}

///// Types for contactUsPage //////
//////////////////////////////////////////////////

export type Branch = {
    id: number;
    name: string;
    country: string;
    address: string;
    email: string;
    phone: string;
}

export type FormData = {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    company: string;
    role: string;
    message: string;
}

export type BranchLocationCardProps = {
    name: string;
    country: string;
    address: string;
    email: string;
    phone: string;
}