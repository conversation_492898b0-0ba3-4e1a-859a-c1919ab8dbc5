import PageHero from '@/components/layout/PageHero';
import NextProject from '@/components/sections/projects/NextProject';
import ProjectDescription from '@/components/sections/projects/ProjectDescription';
import ProjectOverview from '@/components/sections/projects/ProjectOverview';
import { projects } from '@/data/projects';
import { redirect } from 'next/navigation';


export async function generateStaticParams() {
    return projects.map((project) => ({
        projectId: project.id.toString(),
    }));
}

export const revalidate = 60;

const Page = async ({ params }: { params: Promise<{ projectId: string }> }) => {
    const { projectId } = await params;
    const project = projects.find(project => project.id === parseInt(projectId));
    // console.log(project);

    if (!project) {
        redirect('/404');
    }

    return (
        <div>
            <PageHero
                backgroundImage="/images/serviceDetailsBackground.jpg"
                title="Project Page template "
                breadcrumb={{
                    links: [
                        { label: "Home", href: "/" },
                        { label: "Projects", href: "/projects" },
                        { label: "Project Page template ", href: `/projects/${projectId}` }
                    ]
                }}
                height="h-[550px]"
            />
            <ProjectOverview
                {...project}
            />
            <ProjectDescription  {...project}
            />
            <NextProject
                title="Lorem ipsum dolor sit amet consectetur. Tincidunt sed posuere donec"
                service="Service"
                subService="sub service"
                imageUrl="/images/serviceDetailsBackground.jpg"
                projectUrl="/projects/next-project-slug"
            />
        </div>
    );
};

export default Page;
