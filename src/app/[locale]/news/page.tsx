import PageHero from '@/components/layout/PageHero'
import FeaturedStory from '@/components/sections/news/FeaturedStory'
import NewsGallery from '@/components/sections/news/NewsGallery'
import React from 'react'

const News = () => {
    return (
        <div>
            <PageHero
                backgroundImage="/images/aboutBackground.png"
                title="News"
                breadcrumb={{
                    links: [
                        { label: "Home", href: "/" },
                        { label: "News", href: "/news" }
                    ]
                }}
                height="h-[550px]"
            />
            <FeaturedStory
                image='/images/newsHeroImg.jpg'
                title='News Title '
                date='1/1/2025'
                description='Lorem ipsum dolor sit amet consectetur. Cras adipiscing sed nec arcu diam integer molestie sagittis amet. Ultricies mi nulla vulputate quis porta. '
            />
            <NewsGallery />
        </div>
    )
}

export default News