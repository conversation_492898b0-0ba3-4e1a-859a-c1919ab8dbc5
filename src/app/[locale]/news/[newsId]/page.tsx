import { redirect } from 'next/navigation';
import React from 'react'
import { news } from '@/data/news';
import PageHero from '@/components/layout/PageHero';
import AutoSlider from '@/components/sections/news/AutoSlider';
import NewsSectionDetails from '@/components/sections/news/NewsSectionDetails';


export async function generateStaticParams() {
    return news.map((news) => ({
        newsId: news.id.toString(),
    }));
}

export const revalidate = 60;

const NewsDetails = async ({ params }: { params: Promise<{ newsId: string }> }) => {
    const { newsId } = await params;
    const newsItem = news.find(news => news.id === parseInt(newsId));

    if (!newsItem) {
        redirect('/404');
    }

    return (
        <div>
            <PageHero
                backgroundImage="/images/aboutBackground.png"
                title='News Title'
                breadcrumb={{
                    links: [
                        { label: "Home", href: "/" },
                        { label: "News", href: "/news" },
                        { label: newsItem.title, href: `/news/${newsItem.id}` }
                    ]
                }}
                height="h-[550px]"
            />
            <AutoSlider images={Array.isArray(newsItem.image) ? newsItem.image : [newsItem.image]} />
            <NewsSectionDetails {...newsItem} />
        </div>
    )
}

export default NewsDetails