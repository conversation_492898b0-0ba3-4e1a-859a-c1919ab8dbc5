import PageHero from '@/components/layout/PageHero';
import Experience from '@/components/sections/services/Experience';
import CardGrid from '@/components/sections/services/ServiceCardSection';
import ServiceDetailsSlider from '@/components/sections/services/ServiceDetailsSlider';
import ServiceOverview from '@/components/sections/services/ServiceOverview';
import ServicesListDetailsSectionSlider from '@/components/sections/services/ServicesListDetailsSectionSlider';
import { servicesData, slides } from '@/data/services';
import { redirect } from 'next/navigation';

export async function generateStaticParams() {
    return slides.map((slide) => ({
        serviceId: slide.id.toString(),
    }));
}

export const revalidate = 60;

const Page = async ({ params }: { params: Promise<{ serviceId: string }> }) => {
    const { serviceId } = await params;
    const service = slides.find(slide => slide.id === parseInt(serviceId));

    if (!service) {
        redirect('/404');
    }

    return (
        <div>
            <PageHero
                backgroundImage="/images/serviceDetailsBackground.jpg"
                title="Service Page template"
                breadcrumb={{
                    links: [
                        { label: "Home", href: "/" },
                        { label: "Services", href: "/services" },
                        { label: "Service Page template ", href: `/services/${serviceId}` }

                    ]
                }}
                height="h-[550px]"
            />
            <ServiceDetailsSlider />
            <ServiceOverview />
            <CardGrid />
            <Experience />
            <ServicesListDetailsSectionSlider services={servicesData} />
        </div>
    );
};

export default Page;
