import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import { setRequestLocale } from 'next-intl/server';
import type { Metadata } from "next";
import { Poppins } from 'next/font/google'

import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";



const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-poppins',
})


export const metadata: Metadata = {
  title: "Namma",
  description: "Generated by create next app",
};

// export function generateStaticParams() {
//   return routing.locales.map((locale) => ({ locale }));
// } 

export default async function RootLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  // Ensure that the incoming `locale` is valid
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'}>
      <body
        className={`antialiased min-h-screen ${poppins.variable}`}
        suppressHydrationWarning
      >
        <NextIntlClientProvider messages={messages}>
          <Header/>
          {children}
          <Footer />
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
