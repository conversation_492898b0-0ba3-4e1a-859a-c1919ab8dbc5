'use client';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '../ui/Button';

const Footer = () => {
    return (
        <footer
            className="w-full text-white overflow-hidden mt-35 md:mt-0"
            style={{
                backgroundImage: "url('/images/footer.png')",
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                minHeight: '100%',
                width: '100%'
            }}
        >
            {/* Main Footer Content */}
            <div className="container mx-auto px-4 py-16">
                <div className="flex flex-col items-center text-center">

                    <h2 className="text-3xl font-semibold mb-4">Let's Build Success Together</h2>
                    <p className="max-w-lg mx-auto mb-8">
                        Lorem ipsum dolor sit amet consectetur. Phasellus arcu neque porttitor varius.Lorem ipsum dolor sit amet
                    </p>

                    {/* Contact Button */}
                    <Link href="/contact">
                        <Button variant="outline">Contact us</Button>
                    </Link>

                    {/* Divider Line */}
                    <div className="sm:w-[80%] h-[0.5px] bg-[#CFCFCF] bg-opacity-30 my-10"></div>

                    {/* Logo and Social Section */}
                    <div className="flex flex-row justify-between items-center w-[80%]">
                        {/* Logo */}
                        <div className="mb-3 md:mb-0">
                            <Image
                                className='md:w-20 md:h-20 w-15 h-15'
                                src="/images/navLogo.png"
                                alt="Namaa Logo"
                                width={120}
                                height={60}
                                // style={{ width: 'auto', height: 'auto' }}
                            />
                        </div>

                        {/* Social Media */}
                        <div className="flex flex-col items-center mb-8 md:mb-0">
                            <h3 className="text-xl font-medium mb-4">Follow Us</h3>
                            <div className="flex space-x-1 md:space-x-4">
                                <Link href="/" target="_blank"
                                    className="bg-opacity-20 md:p-3 rounded-full hover:bg-opacity-30 transition-all duration-300">
                                    <Image
                                        src='/images/faceIcon.svg'
                                        alt={'facebook Icon'}
                                        className="md:w-full md:h-full object-cover"
                                        width={20}
                                        height={20}
                                    />
                                </Link>
                                <Link href="/" target="_blank"
                                    className="bg-opacity-20 md:p-3 rounded-full hover:bg-opacity-30 transition-all duration-300">
                                    <Image
                                        src='/images/instaIcon.svg'
                                        alt={'instagram Icon '}
                                        className="md:w-full md:h-full object-cover"
                                        width={20}
                                        height={20}
                                    />
                                </Link>
                                <Link href="/" target="_blank"
                                    className="bg-opacity-20  md:p-3 rounded-full hover:bg-opacity-30 transition-all duration-300">
                                    <Image
                                        src='/images/twitterIcon.svg'
                                        alt={'twitter Icon'}
                                        className="md:w-full md:h-full object-cover"
                                        width={20}
                                        height={20}
                                    />
                                </Link>
                                <Link href="/" target="_blank"
                                    className="bg-opacity-20 md:p-3 rounded-full hover:bg-opacity-30 transition-all duration-300">
                                    <Image
                                        src='/images/youtubeIcon.svg'
                                        alt={'youtube Icon'}
                                        className="md:w-full md:h-full object-cover"
                                        width={20}
                                        height={20}
                                    />
                                </Link>
                            </div>
                        </div>

                        {/* Scroll to Top */}
                        <div className="flex flex-col items-center">
                            <button
                                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                                className="flex flex-col items-center hover:text-[#9be0c8] transition-colors duration-300 cursor-pointer"
                            >
                                <Image
                                    src='/images/scrollTopIcon.svg'
                                    alt={'scrollTop Icon'}
                                    className="w-[15%] h-[15%] md:w-[20%] md:h-[20%] object-cover"
                                    width={20}
                                    height={20}
                                />
                                <div className="flex flex-col items-center ">
                                    <span className="md:text-sm text-xs font-medium">Scroll to Top</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Copyright Bar */}
            <div className="w-full py-4 text-center">
                <p className="text-sm">All Rights Reserved © Namaa 2025</p>
            </div>

        </footer>
    );
};

export default Footer;