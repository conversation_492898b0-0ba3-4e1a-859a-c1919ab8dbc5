"use client"

import { PageHeroProps } from '@/types';
import Link from 'next/link';
import Image from 'next/image';


const PageHero = ({
    backgroundImage,
    title,
    subtitle,
    breadcrumb,
    height = "h-[500px]",
}: PageHeroProps) => {
    return (
        <div className="w-full relative mb-[5%]">
            {/* Background image */}
            <div
                className={`w-full ${height} relative`}
                style={{
                    backgroundImage: `url(${backgroundImage})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                }}
            >
                {/* Blue gradient overlay */}
                <div
                    className="absolute inset-0"
                    style={{
                        background: 'linear-gradient(180deg, rgba(48, 82, 125, 0.90) 0%, rgba(48, 82, 125, 0.00) 100%)',
                    }}
                ></div>

                {/* White gradient overlay */}
                <div
                    className="absolute bottom-0 left-0 right-0 h-1/4 bg-gradient-to-t from-white to-white/0"
                ></div>
            </div>

            {/* Title and subtitle section with centered content */}
            <div className="absolute -bottom-10 left-0 right-0 px-4">
                <div className="max-w-screen-xl mx-auto text-center">
                    <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#365D92] mb-4">
                        {title}
                    </h1>

                    {subtitle && (
                        <p className="text-lg md:text-xl text-black max-w-2xl mx-auto">
                            {subtitle}
                        </p>
                    )}
                </div>
            </div>

            {/* Breadcrumb navigation (if provided) */}
            {breadcrumb && (
                <div className="absolute -bottom-20 left-0 right-0 pb-4">
                    <div className="max-w-screen-xl mx-auto px-4 flex justify-center items-center">
                        <nav className="flex items-center space-x-2 text-black md:text-base text-xs">
                            {breadcrumb.links.map((link, index, array) => (
                                <div key={link.href} className="flex items-center font-bold">
                                    <Link
                                        href={link.href}
                                        className={`hover:cursor-pointer transition-colors ${index === array.length - 1 ? 'text-[#365D92]' : 'text-[#B8B8B8]'}`}
                                    >
                                        {link.label}
                                    </Link>
                                    {index < array.length - 1 && (
                                        <Image
                                            src="/images/rightNavArrow.svg"
                                            alt="right Nav Arrow.svg"
                                            width={20}
                                            height={20}
                                            className="w-4 h-4"
                                            priority
                                        />
                                    )}
                                </div>
                            ))}
                        </nav>
                    </div>
                </div>
            )}
        </div>
    );
};

export default PageHero;