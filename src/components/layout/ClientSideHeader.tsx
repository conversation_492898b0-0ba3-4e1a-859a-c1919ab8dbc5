"use client"

import { useState, useEffect } from "react";
import Image from 'next/image';
import { AlignJustify, X } from "lucide-react";

import { useRouter, usePathname, Link } from '@/i18n/routing';
import { useParams } from 'next/navigation';
import { useTransition } from 'react';
import { useTranslations } from "next-intl";

const ClientSideHeader = () => {
    const t = useTranslations('Navigation');
    const pathname = usePathname()
    const [scrolled, setScrolled] = useState(false);
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const textColor = mobileMenuOpen ? 'text-white' : scrolled ? 'text-black' : 'text-white';
    const router = useRouter();
    const params = useParams();
    const [isPending, startTransition] = useTransition();
    const currentLocale = params.locale as string;

    const handleLanguageChange = (locale: string) => {
        startTransition(() => {
            // Navigate to the same path but with different locale
            router.replace(pathname, { locale });
        });
    };


    useEffect(() => {
        const handleScroll = () => {
            setScrolled(window.scrollY > 30);
        };

        window.addEventListener("scroll", handleScroll);
        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, []);

    const navLinks = [
        { label: 'home', href: '/' },
        { label: 'about', href: '/about-us' },
        { label: 'services', href: '/services' },
        { label: 'equipment', href: '/equipment' },
        { label: 'projects', href: '/projects' },
        { label: 'news', href: '/news' },
        { label: 'careers', href: '/careers' },
        { label: 'contact', href: '/contact-us' },
    ];



    return (
        <nav
            className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${mobileMenuOpen ? 'bg-black/90 backdrop-blur-2xl' : 'bg-transparent/95 backdrop-blur-md'} shadow-[0px_4px_51.1px_0px_rgba(0,0,0,0.09)]`}
            style={{
                height: scrolled ? '60px' : '80px',
                width: '100%',
                margin: 0,
                padding: 0
            }}
        >
            <div className="w-full max-w-screen-2xl mx-auto px-4 sm:px-6 md:px-8 flex justify-between items-center h-full">
                <Link href="/" className="flex items-center gap-2">
                    <div className="flex-shrink-0">
                        <Image
                            src="/images/navLogo.png"
                            alt="Company Logo"
                            width={30}
                            height={30}
                            className="w-14 h-14"
                            priority
                        />
                    </div>
                </Link>

                {/* Desktop navigation  */}
                <div className="hidden lg:flex items-stretch h-full">
                    {navLinks.map((link) => (
                        <Link
                            key={link.href}
                            href={link.href}
                            className={`text-lg font-medium ${textColor} hover:bg-[#30527d] transition-colors px-4 flex items-center h-full ${pathname !== '/' && pathname === link.href ? 'bg-[#30527d]' : ''}`}
                        >
                            <span>{t(link.label)}</span>
                        </Link>
                    ))}
                    {/* Language selector */}
                    <button
                        onClick={() => handleLanguageChange(currentLocale === 'en' ? 'ar' : 'en')}
                        className={`ms-4 flex items-center cursor-pointer hover:opacity-80 transition-opacity`}
                        disabled={isPending}
                    >
                        <div className="w-5 h-5 relative mr-1">
                            <Image src='/images/icon.svg' alt={'Language icon'} width={80} height={80} />
                        </div>
                        <span
                            className={`text-base font-medium ${textColor}`}
                            suppressHydrationWarning
                        >
                            {currentLocale === 'en' ? 'AR' : 'EN'}
                        </span>
                    </button>
                </div>

                {/* Mobile menu button */}
                <button
                    className={`lg:hidden ${textColor} p-2 hover:cursor-pointer rounded-md transition-colors`}
                    onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                    aria-label="Toggle menu"
                >
                    {mobileMenuOpen ? (
                        <X className="w-6 h-6 sm:w-7 sm:h-7" color="white" />
                    ) : (
                        <AlignJustify className="w-6 h-6 sm:w-7 sm:h-7" />
                    )}
                </button>
            </div>

            {/* Mobile menu */}
            <div className={`${mobileMenuOpen ? "block" : "hidden"} [@media(min-width:1030px)]:hidden absolute top-full left-0 w-full h-screen pt-[20%] p-14 space-y-4 text-center transition-all duration-300 bg-black/90 backdrop-blur-2xl shadow-[0px_4px_51.1px_0px_rgba(0,0,0,0.25)]`}>
                {navLinks.map((link) => (
                    <Link
                        key={link.href}
                        href={link.href}
                        className={`
                            block
                            text-lg
                            font-medium
                            hover:text-primary-600
                            transition-colors
                            hover:underline
                            ${textColor} ${pathname !== '/' && pathname === link.href ? 'bg-[#30527d]' : ''}
                        `}
                        onClick={() => setMobileMenuOpen(false)}
                    >
                        {t(link.label)}
                    </Link>
                ))}
            </div>
        </nav>
    );
};

export default ClientSideHeader;