import React from 'react';
import { CardProps } from "@/types"; 

const PartnerCard = React.forwardRef<HTMLDivElement, CardProps>(({ 
    className = '',
    children,
}, ref) => { 

    const baseClasses = "rounded-3xl overflow-hidden w-[200px] sm:w-[250px] md:w-[300px] h-32 md:h-60 relative bg-[#E0E5EC] flex items-center justify-center";

    // Combine all classes
    const cardClasses = [
        baseClasses,
        className
    ].join(' ').trim();

    return (
        <div className={cardClasses} ref={ref}> 
            {children}
        </div>
    );
});

PartnerCard.displayName = 'PartnerCard'; 

export { PartnerCard };