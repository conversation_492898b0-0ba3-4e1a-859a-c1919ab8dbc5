"use client"

import { InfoCardProps } from '@/types';
import Image from 'next/image';
import { useState } from 'react';


const InfoCard = ({
    icon,
    hoverIcon = icon,
    title,
    primaryText,
}: InfoCardProps) => {
    const [isHovered, setIsHovered] = useState(false);

    return (
        <div
            className="relative flex flex-col w-44 h-40 m-4 text-center items-center justify-center cursor-pointer"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            {/* Top-right bracket */}
            <div className="absolute -top-4 -right-4 w-12 h-12">
                <Image
                    src={isHovered ? "/images/projectDetailsRightIconHover.svg" : "/images/projectDetailsIconRight.svg"}
                    alt="project-details-Right icon"
                    width={32}
                    height={32}
                    className="w-12 h-12 object-contain"
                />
            </div>

            {/* Icon and Title Container */}
            <div className="flex flex-row gap-4 items-center justify-around">
                <Image
                    src={isHovered ? hoverIcon : icon}
                    alt={`${title} icon`}
                    width={32}
                    height={32}
                    className="w-12 h-12"
                />
                {/* Title */}
                <h3 className="text-stone-900 text-base font-semibold">
                    {title}
                </h3>
            </div>

            {/* Primary Text */}
            <p className="text-black text-xl font-medium mt-7">
                {primaryText}
            </p>

            {/* Bottom-left bracket */}
            <div className="absolute -bottom-4 -left-4 w-12 h-12">
                <Image
                    src={isHovered ? "/images/projectDetailsLeftIconHover..svg" : "/images/projectDetailsIconLeft.svg"}
                    alt="project-details-Left icon"
                    width={32}
                    height={32}
                    className="w-12 h-12 object-contain"
                />
            </div>
        </div>
    );
};

export default InfoCard