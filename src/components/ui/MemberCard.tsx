"use client"
import { useState, useRef } from 'react';
import Image from 'next/image';
import MemberModal from '../sections/aboutPage/MemberModal';
import { MemberCardProps } from '@/types';


const MemberCard = ({
    imageUrl,
    name,
    title,
    className = '',
    about,
    achievements,
    links
}: MemberCardProps) => {
    const [showModal, setShowModal] = useState(false);

    return (
        <>
            <div
                className={`group relative rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 ${className} hover:cursor-pointer w-full max-w-[280px] mx-auto mb-[20%]`}
                onClick={() => setShowModal(true)}
            >
                {/* Background Image */}
                <div className="relative w-full h-48 sm:h-72">
                    <Image
                        src={imageUrl}
                        alt={name}
                        fill
                        className="object-cover transition-transform duration-500 rounded-2xl"
                    />
                </div>

                {/* Member Info */}
                <div
                    className="absolute w-[85%] left-1/2 -translate-x-1/2 -bottom-10 
                    px-6 py-4 
                    bg-[#365D92] 
                    group-hover:bg-gradient-to-br 
                    group-hover:from-[#54AD84] 
                    group-hover:to-[#6798D4]
                    text-white rounded-2xl sm:rounded-3xl overflow-hidden text-center 
                    transition-all duration-300"
                >
                    <h3 className="text-sm sm:text-lg font-medium leading-tight truncate max-w-full">{name}</h3>
                    <p className="text-xs sm:text-base font-normal mt-0.5 sm:mt-1 truncate max-w-full">{title}</p>
                </div>
            </div>

            <MemberModal
                isOpen={showModal}
                onClose={() => setShowModal(false)}
                member={{
                    imageUrl,
                    name,
                    title,
                    about,
                    achievements,
                    links
                }}
            />
        </>
    );
};

export default MemberCard;