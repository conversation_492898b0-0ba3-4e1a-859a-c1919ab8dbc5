"use client"

import { AccordionProps } from "@/types";
import { Plus } from "lucide-react";
import { useState, useRef, useEffect } from "react";

const Accordion = ({
    title,
    children,
    isOpen: defaultOpen = false,
    className = "",
}: AccordionProps) => {
    const [isOpen, setIsOpen] = useState(defaultOpen);
    const [height, setHeight] = useState("0px");
    const contentRef = useRef<HTMLDivElement>(null);


    useEffect(() => {
        if (contentRef.current) {
            const scrollHeight = contentRef.current.scrollHeight;
            setHeight(isOpen ? `${Math.max(scrollHeight, 50)}px` : "0px");
        }
    }, [isOpen]);


    return (
        <div className="mb-2 bg-gradient-to-r from-green-500/0 to-slate-500/0 overflow-hidden">
            <button
                className={`bg-default hover:bg-gray1 flex w-full cursor-pointer items-center justify-between p-4 text-start ${isOpen ? "" : "border-b-[0.40px] border-[#365D92]"
                    } transition-all duration-200 ease-out`}
                onClick={() => setIsOpen(!isOpen)}
            >
                <span className="text-base font-medium text-[#365D92]">{title}</span>
                <span
                    className={`transform transition-transform duration-200 ease-out ${isOpen ? "rotate-45" : "rotate-0"
                        }`}
                >
                    <Plus color="#365D92" />
                </span>
            </button>

            <div
                ref={contentRef}
                className={`${className} bg-gradient-to-r from-green-500/0 to-slate-500/0 border-b-[0.40px] border-[#365D92] transition-all duration-300 ease-in-out overflow-hidden`}
                style={{
                    maxHeight: height,
                    opacity: isOpen ? 1 : 0,
                    padding: isOpen ? "16px" : "0 16px",
                }}
            >
                {children}
            </div>
        </div>
    );
};

export default Accordion;
