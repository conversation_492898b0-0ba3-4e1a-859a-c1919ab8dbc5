import React from 'react';
import Image from 'next/image';
import { CalendarDays } from 'lucide-react';
import { LatestCardProps } from '@/types';


const LatestNewsCard = ({
    image,
    title,
    date,
    description,
    className = '',
    ...props
}: LatestCardProps) => {
    // Base card styles
    const baseClasses = "w-5/6 p-2 inline-flex flex-col justify-start items-center gap-2 group cursor-pointer";

    // Combine all classes
    const cardClasses = [
        baseClasses,
        className
    ].join(' ');

    return (
        <div className={cardClasses} {...props}>
            {/* Card Image */}
            <div className="self-stretch h-80 relative rounded-lg shadow-md overflow-hidden">
                <Image
                    src={image}
                    alt={title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
            </div>

            {/* Title and Date */}
            <div className="self-stretch flex flex-col justify-start items-start gap-1">
                <div className="self-stretch justify-start text-[#8C8C8C] group-hover:text-[#30527D] text-base font-medium font-['Poppins']">
                    {title}
                </div>
                <div className="inline-flex justify-start items-center gap-1">
                    <CalendarDays size={18} />
                    <div className="text-center justify-start text-[#8C8C8C] text-[10px] font-medium font-['Poppins']">
                        {typeof date === 'string' ? date : date.toLocaleDateString()}
                    </div>
                </div>
            </div>

            {/* Description */}
            <div className="self-stretch h-8 justify-start text-[#8C8C8C] group-hover:text-[#30527D] text-sm font-normal font-['Poppins'] group-hover:underline">
                {description}
            </div>
        </div>
    );
};

export { LatestNewsCard as LatestCard };