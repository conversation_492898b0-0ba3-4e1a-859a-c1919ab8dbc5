import React from 'react';
import { ArrowRight } from 'lucide-react';

export type ButtonProps = {
    key?: number;
    ariaLabel?: string
    className?: string;
    variant?: 'primary' | 'secondary' | 'outline' | 'outlineGradient';
    size?: 'md' | 'lg';
    hasArrow?: boolean;
    onClick?: React.MouseEventHandler<HTMLButtonElement>;
    children?: React.ReactNode;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    ({
        className = '',
        variant = 'primary',
        size = 'md',
        hasArrow: withArrow = false,
        children,
        ...props
    }, ref) => {
        // Base button styles
        const baseClasses = "group relative inline-flex items-center justify-center rounded-md font-medium transition-all duration-300 overflow-hidden focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none cursor-pointer";

        // Variant styles
        const variantClasses = {
            primary: "bg-[#30527d] hover:bg-[#3B608E] text-white",
            secondary: "bg-white text-[#3B608E] border border-[#3B608E]",
            outline: "bg-[#9ED9B8] text-[#30527d]",
            outlineGradient: "bg-transparent text-current relative hover:text-white"
        };

        // Size styles
        const sizeClasses = {
            md: "text-xs sm:text-base px-4 sm:px-6 py-2 sm:py-3 min-w-32 sm:min-w-44 h-10 sm:h-14",
            lg: "text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 min-w-36 sm:min-w-48 h-12 sm:h-16",
        };

        // Combine all classes
        const buttonClasses = [
            baseClasses,
            variantClasses[variant],
            sizeClasses[size],
            className
        ].join(' ');

        // Gradient styles (reusable)
        const gradientStyle = {
            background: 'linear-gradient(186deg, #54AD84 4.43%, #6798D4 134.03%)'
        };

        return (
            <button
                ref={ref}
                className={buttonClasses}
                {...props}
            >
                {/* Gradient border and hover background for outlineGradient variant */}
                {variant === 'outlineGradient' && (
                    <>
                        {/* Gradient border (always visible) */}
                        <span 
                            className="absolute inset-0 rounded-md p-[1px]"
                            style={{
                                ...gradientStyle,
                                WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                                WebkitMaskComposite: 'xor',
                                maskComposite: 'exclude',
                                pointerEvents: 'none'
                            }}
                        />
                        {/* Gradient background (visible on hover) */}
                        <span 
                            className="absolute inset-0 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                            style={gradientStyle}
                        />
                    </>
                )}
                
                <div className={`flex items-center justify-center ${withArrow ? "transition-transform duration-300 group-hover:-translate-x-3" : ""} me-2.5 relative z-10`}>
                    <span className='flex flex-row items-center-safe'>
                        {children}
                    </span>
                </div>
                {withArrow && (
                    <ArrowRight className="absolute right-5 transform translate-x-8 opacity-0 transition-all duration-300 group-hover:translate-x-0 group-hover:opacity-100 z-10" />
                )}
            </button>
        );
    }
);

Button.displayName = 'Button';

export { Button };