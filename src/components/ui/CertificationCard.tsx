"use client"

import { CertificationCardProps } from "@/types";
import { CalendarIcon } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

const CertificationCard = ({ isoNumber, date, className = "" }: CertificationCardProps) => {
    const [isHovered, setIsHovered] = useState(false);

    return (
        <div
            className={`w-80 h-48 bg-white rounded-2xl shadow-[0px_4px_24.899999618530273px_0px_rgba(0,0,0,0.12)] p-4 flex items-center justify-around ${className}`}
            style={{
                backgroundImage: isHovered ? 'url(/images/certifiedCardBackground.svg)' : 'none',
                backgroundPosition: 'cover',
                backgroundRepeat: 'no-repeat',
                backgroundSize: '320px 192px',
            }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <Image
                src={isHovered ? "/images/certificateIcon1.svg" : "/images/certificateIcon.svg"}
                alt="about-icon"
                width={24}
                height={24}
                priority
                className='md:w-16 md:h-24'
            />
            <div className={`flex flex-col items-start gap-4 ${isHovered ? 'text-white' : 'text-[#365D92]'}`}>
                {/* ISO Number */}
                <h3 className={`text-2xl font-bold `}>{isoNumber}</h3>
                {/* Date */}
                <div className="flex items-center gap-2 ">
                    <CalendarIcon size={16} />
                    <span className="text-sm font-medium">{date}</span>
                </div>
            </div>
        </div>
    );
};

export default CertificationCard;