import { LucideProps } from "lucide-react";
import Image from "next/image";
import React from "react";
import { ForwardRefExoticComponent, RefAttributes } from "react";


export type CardProps = {
    imageUrl: string;
    title: string;
    icon?: ForwardRefExoticComponent<Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>>;
    alt?: string;
    className?: string;
} & React.HTMLAttributes<HTMLDivElement>;

const ServiceCard = ({
    imageUrl,
    title,
    icon,
    alt = "Card image",
    className = '',
    ...props
}: CardProps) => {
    // Base card styles
    const baseClasses = "relative w-full h-64 overflow-hidden rounded-lg group";

    // Combine all classes
    const cardClasses = [
        baseClasses,
        className
    ].join(' ');

    return (
        <div className={cardClasses} {...props}>
            {/* Background Image */}
            <div className="absolute inset-0 w-full h-full bg-gray-300">
                <Image
                    src={imageUrl}
                    alt={alt}
                    className="w-full h-full object-cover"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    priority
                />
            </div>

            {/* Hover Overlay - Green with low opacity */}
            <div className="absolute inset-0 bg-green-700/70 bg-opacity-60 opacity-0 transition-opacity duration-300 group-hover:opacity-50 flex flex-col items-center justify-center">
                {/* Icon */}
                {icon && (
                    <div className="text-white mb-2">
                        {icon && (
                            <div className="text-white mb-2">
                                {React.createElement(icon)}
                            </div>
                        )}
                    </div>
                )}
                {/* Title */}
                <h3 className="text-white text-xl font-semibold text-center">{title}</h3>
            </div>

        </div>
    );
};

export { ServiceCard  };