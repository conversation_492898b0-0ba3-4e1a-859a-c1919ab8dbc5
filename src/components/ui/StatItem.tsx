'use client';

import { StatItemProps } from '@/types';
import { useEffect, useRef, useState } from 'react';

const StatItem = ({ endValue, title, description, suffix = '' }: StatItemProps) => {
    const [count, setCount] = useState(0);
    const elementRef = useRef<HTMLDivElement>(null);

    // Simple counting function
    const startCounting = () => {
        // Calculate step size based on number magnitude
        const duration = 2000; // ms
        const steps = 50;
        const stepTime = duration / steps;
        const increment = endValue / steps;

        let current = 0;
        const timer = setInterval(() => {
            current += increment;
            if (current >= endValue) {
                setCount(endValue);
                clearInterval(timer);
            } else {
                setCount(Math.floor(current));
            }
        }, stepTime);
    };

    useEffect(() => {
        const observer = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting) {
                startCounting();
                observer.disconnect();
            }
        }, { threshold: 0.1 });

        if (elementRef.current) {
            observer.observe(elementRef.current);
        }

        return () => observer.disconnect();
    }, []);



    return (
        <div className="px-4 text-center" ref={elementRef}>
            <div className="text-5xl font-medium text-[#30527d] flex justify-center items-center">
                <span>{count.toLocaleString()}</span>
                <span className="text-3xl ms-1">{suffix}</span>
            </div>
            <h3 className="self-stretch opacity-80 text-center justify-start   text-base font-medium text-[#30527d]">{title}</h3>
            <p className="text-[#666666] text-[10px]font-normal mt-2 max-w-xs mx-auto">{description}</p>
        </div>
    );
};

export default StatItem;