"use client";

import { ProjectCardProps } from "@/types";
import { ArrowUpRight } from "lucide-react";
import { useState } from "react";
import Link from 'next/link';
import Image from 'next/image';


const ProjectCard = ({ project }: ProjectCardProps) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="w-full max-w-md mx-auto rounded-4xl">
      <Link href={`/projects/${project.id}`}>
        <div
          className="group relative transition-all duration-500 cursor-pointer"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Image Container */}
          <div className="relative h-auto md:h-80 overflow-hidden rounded-2xl ">
            <Image
              src={project.image[0]}
              alt={project.name}
              width={10}
              height={10}
              className="w-full h-full transition-transform duration-700 ease-out z-20"
            />

            {/* Base gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent rounded-3xl " />

            {/* Arrow Icon */}
            <div
              className={`absolute top-0 -right-0 w-15 h-15 rounded-[5px_16px] flex items-center justify-center transition-all duration-300 border-1 shadow-[0px_0px_1px_6px_white] ${
                isHovered
                  ? "bg-gradient-to-b from-[#54AD84] to-[#6798D4] text-white"
                  : "bg-white text-[#39A171] "
              }`}
            >
              <ArrowUpRight className="w-5 h-5" />
            </div>
          </div>

          {/* Content Overlay */}
          <div
            className={`absolute bottom-13 left-0 right-0 p-6 pt-18  h-28 text-white transform transition-all duration-500 ease-out -z-10  ${
              isHovered
                ? "translate-y-25 opacity-100"
                : "-translate-y opacity-0"
            }`}
          >
            {/* Background with gradient that matches the image overlay */}
            <div className="absolute inset-0 bg-gradient-to-b from-[#54AD84] to-[#6798D4] rounded-2xl font-medium" />

            {/* Content */}
            <div className="relative z-10 flex flex-row justify-between  text-white">
              <h3 className="text-base">{project.name}</h3>
              <span className="text-[10px]">{project.sector}</span>
              <span className="text-[10px]">{project.location}</span>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default ProjectCard;
