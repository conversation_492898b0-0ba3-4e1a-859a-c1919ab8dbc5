import React from 'react';
import Image from 'next/image';
import { PerformanceHighlightsProp } from '@/types';


const PerformanceCard = ({
    icon,
    title,
    description
}: PerformanceHighlightsProp) => {
    return (
        <div className="relative md:w-48 md:h-50 flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-[0px_2px_14.899999618530273px_0px_rgba(0,0,0,0.15)] hover:cursor-pointer hover:shadow-md transition-shadow duration-200">
            {/* Icon Circle */}
            <div className="absolute -top-7 w-16 h-16 bg-[#003E8D1A] rounded-full flex items-center justify-center mb-4">
                <Image
                    src={icon}
                    alt={title}
                    width={32}
                    height={32}
                    className="object-contain"
                />
            </div>

            {/* Title */}
            <h3 className="mt-4 text-base font-medium text-zinc-800 mb-3">
                {title}
            </h3>

            {/* Description */}
            <p className="text-xs font-normal text-zinc-800">
                {description}
            </p>
        </div>
    );
};

export default PerformanceCard;