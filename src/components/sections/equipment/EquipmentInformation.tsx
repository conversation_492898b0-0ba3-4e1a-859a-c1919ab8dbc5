import React from 'react';
import FeatureCard from './FeatureCard';
import { features } from '@/data/equipment';


const EquipmentInformation = () => {

    return (
        <section className="py-8 sm:py-12 px-4 md:px-6 lg:px-8">
            <div className="container max-w-7xl mx-auto">
                {/* Hero Content */}
                <div className="flex flex-col lg:flex-row gap-12 mb-16">
                    {/* Main Heading */}
                    <div className="lg:w-1/2 mt-5 sm:-mt-4">
                        <h3 className="text-2xl md:text-3xl lg:text-4xl font-semibold text-black leading-[60.38px]">
                            <span className="text-[#54AD84]">Lorem ipsum</span>{' '}
                            <span className="text-black">commodo consequat eget at.</span>
                        </h3>
                    </div>

                    {/* Description Columns */}
                    <div className="lg:w-1/2 flex flex-col md:flex-row gap-8 text-[#5F5F5F] text-sm font-semibold">
                        <p className="md:w-1/2 leading-normal">
                            Lorem ipsum dolor sit amet consectetur. Vel nunc bibendum amet sed.
                            Cursus at aliquam blandit faucibus at. Dolor malesuada imperdiet id
                            feugiat nunc. Proin ullamcorper nisl id risus adipiscing in
                        </p>
                        <p className="md:w-1/2 leading-normal">
                            Lorem ipsum dolor sit amet consectetur. Vel nunc bibendum amet sed.
                            Cursus at aliquam blandit faucibus at. Dolor malesuada imperdiet id
                            feugiat nunc. Proin ullamcorper nisl id risus adipiscing in
                        </p>
                    </div>
                </div>

                {/* Feature Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {features.map((feature, index) => (
                        <FeatureCard
                            key={index}
                            name={feature.name}
                            description={feature.description}
                        />
                    ))}
                </div>
            </div>
        </section>
    );
};

export default EquipmentInformation;