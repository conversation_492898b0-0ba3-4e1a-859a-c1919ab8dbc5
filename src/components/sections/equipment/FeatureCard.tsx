import { FeatureCardProps } from '@/types';
import Image from 'next/image';



const FeatureCard = ({ name, description }: FeatureCardProps) => {
    return (
        <div className="flex items-start space-x-4 p-6 bg-white  rounded-2xl shadow-[0px_5px_20.299999237060547px_0px_rgba(0,0,0,0.08)] overflow-hidden">
            {/* Icon */}
            <div className="flex-shrink-0">
                <Image
                    src={"/images/equpIcon.svg"}
                    alt="ImgCornerLeft icon"
                    width={32}
                    height={32}
                    className="w-8 h-8 sm:w-10 sm:h-10"
                />
            </div>

            {/* Content */}
            <div className="flex-1">
                <h3 className="text-sm font-semibold text-[#30527D] mb-2">
                    {name}
                </h3>
                <p className="text-[#868686] text-sm font-medium leading-relaxed">
                    {description}
                </p>
            </div>
        </div>
    );
};

export default FeatureCard;