'use client'

import { useState } from 'react';
import Image from 'next/image';
import { XIcon } from 'lucide-react';
import { EquipmentModalProps } from '@/types/index'
import { Button } from '@/components/ui/Button';
import ProjectCard from '@/components/ui/ProjectCard';
import PerformanceCard from "./PerformanceCard"

const EquipmentModal = ({
    isOpen,
    onClose,
    equipment
}: EquipmentModalProps) => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [currentProjectIndex, setCurrentProjectIndex] = useState(0);

    if (!isOpen) return null;

    const nextProject = () => {
        if (currentProjectIndex < equipment.relatedProjects.length - 3) {
            setCurrentProjectIndex(prev => prev + 1);
        }
    };

    const prevProject = () => {
        if (currentProjectIndex > 0) {
            setCurrentProjectIndex(prev => prev - 1);
        }
    };


    const canNavigateNext = currentProjectIndex < equipment.relatedProjects.length - 3;
    const canNavigatePrev = currentProjectIndex > 0;

    return (
        <div className="fixed inset-0 z-50 flex items-center flex-col justify-between">
            {/* Backdrop */}
            <div
                className="absolute inset-0 bg-black/10 backdrop-blur-sm"
                onClick={onClose}
            />

            {/* Close Button - Above Modal */}
            <Button
                onClick={onClose}
                className="[@media(min-width:1190px)]:flex hidden absolute top-8 left-122 z-50 text-xl font-semibold"
            >
                <XIcon className="w-5 h-5 text-white me-2" /> Close
            </Button>

            {/* Modal */}
            <div className="relative rounded-lg bg-white shadow-[0px_4px_31.5px_0px_rgba(0,0,0,0.25)] max-w-6xl mb-10 mx-4 overflow-y-auto mt-10 scrollbar-hide p-10">
                <XIcon onClick={onClose} className="absolute right-10 w-6 h-6  text-[#30527D] [@media(min-width:1190px)]:hidden cursor-pointer" />
                {/* Header */}
                <div className="text-center py-6">
                    <h2 className="text-3xl font-bold text-[#30527D]">{equipment.text}</h2>
                </div>

                {/* Main Content */}
                <div className="p-6">
                    {/* Image Gallery Section */}
                    <div className="flex flex-col sm:flex-row gap-6 mb-12">
                        {/* Mobile Carousel View */}
                        <div className="sm:hidden relative w-full h-[300px]">
                            <div className="relative w-full h-full bg-gray-100 rounded-2xl overflow-hidden shadow-[0px_1px_27.700000762939453px_0px_rgba(0,0,0,0.22)]">
                                <Image
                                    src={equipment.imageUrl[currentImageIndex]}
                                    alt={equipment.text}
                                    fill
                                    className="object-cover"
                                />
                            </div>
                            {/* Mobile Navigation Dots */}
                            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
                                {equipment.imageUrl.slice(0, 3).map((_, index) => (
                                    <button
                                        key={index}
                                        onClick={() => setCurrentImageIndex(index)}
                                        className={`w-2.5 h-2.5 rounded-full transition-all ${currentImageIndex === index
                                            ? 'bg-[#30527D] w-4'
                                            : 'bg-gray-300'
                                            }`}
                                    />
                                ))}
                            </div>
                        </div>

                        {/* Desktop Thumbnail View */}
                        <div className="hidden sm:flex flex-col gap-3">
                            {equipment.imageUrl.slice(0, 3).map((img, index) => (
                                <button
                                    key={index}
                                    onClick={() => setCurrentImageIndex(index)}
                                    className={`w-36 h-36 rounded-lg overflow-hidden border-2 transition-all shadow-[0px_1px_27.700000762939453px_0px_rgba(0,0,0,0.22)] ${currentImageIndex === index
                                        ? 'border-[#30527D] shadow-lg'
                                        : 'border-gray-200 hover:border-gray-300'
                                        }`}
                                >
                                    <Image
                                        src={img}
                                        alt={`${equipment.text} view ${index + 1}`}
                                        width={44}
                                        height={44}
                                        className="w-full h-full object-cover"
                                    />
                                </button>
                            ))}
                        </div>

                        {/* Main Image - Desktop Only */}
                        <div className="flex-1 relative hidden sm:block">
                            <div className="relative h-full bg-gray-100 rounded-2xl overflow-hidden shadow-[0px_1px_27.700000762939453px_0px_rgba(0,0,0,0.22)]">
                                <Image
                                    src={equipment.imageUrl[currentImageIndex]}
                                    alt={equipment.text}
                                    fill
                                    className="object-cover"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Performance Highlights */}
                    <div className="mb-8">
                        <h3 className="text-2xl font-semibold text-black mb-15">Performance Highlights</h3>
                        <div className="flex flex-wrap justify-center gap-4">
                            {equipment.PerformanceHighlights.map((highlight, index) => (
                                <PerformanceCard
                                    key={index}
                                    title={highlight.title}
                                    description={highlight.description}
                                    icon={highlight.icon}
                                />
                            ))}
                        </div>
                    </div>

                    {/* Related Projects */}
                    <div className='mt-20'>
                        <h3 className="text-2xl font-semibold text-black mb-6">Used in These Projects</h3>
                        <div className="relative">
                            <div className="flex flex-col sm:flex-row gap-10 overflow-hidden">
                                <div
                                    className="flex flex-col sm:flex-row gap-10 transition-transform duration-500 ease-in-out"
                                    style={{
                                        transform: `translateX(-${currentProjectIndex * (100 / 6)}%)`
                                    }}
                                >
                                    {equipment.relatedProjects.map((project) => (
                                        <div key={project.id} className="w-full sm:w-[30%] flex-shrink-0 max-w-[300px] mx-auto mb-15">
                                            <ProjectCard project={project} />
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Project Navigation */}
                            {equipment.relatedProjects.length > 3 && (
                                <div className='hidden sm:block'>
                                    <button
                                        onClick={prevProject}
                                        disabled={!canNavigatePrev}
                                        className={`absolute left-0 top-1/2 -translate-y-1/2 -translate-x-18 transition-all duration-300 ease-in-out cursor-pointer ${!canNavigatePrev ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'
                                            }`}
                                    >
                                        <Image
                                            src='/images/serviceSliderIcon.svg'
                                            alt={'Previous projects'}
                                            className="w-20 h-20 object-cover"
                                            width={5}
                                            height={5}
                                        />
                                    </button>
                                    <button
                                        onClick={nextProject}
                                        disabled={!canNavigateNext}
                                        className={`absolute right-0 top-1/2 -translate-y-1/2 translate-x-18 transition-all duration-300 ease-in-out cursor-pointer rotate-180 ${!canNavigateNext ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'
                                            }`}
                                    >
                                        <Image
                                            src='/images/serviceSliderIcon.svg'
                                            alt={'Next projects'}
                                            className="w-20 h-20 object-cover"
                                            width={5}
                                            height={5}
                                        />
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default EquipmentModal;