import { equipmentData } from '@/data/equipment'
import EquipmentCard from './EquipmentCard'

const EquipmentGallery = () => {

    return (
        <section className="py-8 sm:py-12 px-4 md:px-6 lg:px-8 ">
            <div className="container mx-auto">
                {/* Header Section */}
                <div className="text-center mb-12">
                    <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-800 mb-4">
                        Our Equipment
                    </h2>
                    <p className="text-base text-gray-600 font-normal max-w-3xl mx-auto">
                        Lorem ipsum dolor sit amet consectetur. Vel nunc bibendum amet sed. Cursus at aliquam blandit
                        faucibus at. Dolor molestudda imperdiet id feugiat nunc. Proin ullamcorper nisl id risus adipiscing in
                    </p>
                </div>

                {/* Equipment gallery */}
                <div className="flex flex-col items-center gap-14 mx-auto">
                    {/* First Row - 3 cards */}
                    <div className="flex flex-wrap justify-center gap-12 w-full">
                        <div className="w-full max-w-xs mx-auto">
                            <EquipmentCard {...equipmentData[0]} />
                        </div>
                        <div className="w-full max-w-xs mx-auto">
                            <EquipmentCard {...equipmentData[1]} />
                        </div>
                        <div className="w-full max-w-xs mx-auto">
                            <EquipmentCard {...equipmentData[2]} />
                        </div>
                    </div>

                    {/* Second Row - 2 cards centered */}
                    <div className="flex flex-wrap justify-around gap-5 md:gap-0 w-full md:w-[80%]">
                        <div className="w-full max-w-xs mb-15 lg:mb-0">
                            <EquipmentCard {...equipmentData[3]} />
                        </div>
                        <div className="w-full max-w-xs ">
                            <EquipmentCard {...equipmentData[4]} />
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default EquipmentGallery