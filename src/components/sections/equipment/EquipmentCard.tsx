'use client'

import React, { useState } from 'react';
import { EquipmentProps } from '@/types';
import Image from 'next/image';
import EquipmentModal from './EquipmentModal';


const EquipmentCard = ({ imageUrl, text, label, PerformanceHighlights, relatedProjects }: EquipmentProps) => {

    const [showModal, setShowModal] = useState(false);

    return (
        <>
            <div className="relative overflow-hidden w-full max-w-xs aspect-square bg-neutral-300 rounded-2xl shadow-lg cursor-pointer group" onClick={() => setShowModal(true)}>
                <div className="w-full h-full relative">
                    <Image
                        src={imageUrl[0]}
                        alt={text}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        fill
                    />
                </div>
                <div className="absolute inset-0 bg-gradient-to-b from-black/0 to-[#30527D] opacity-0 group-hover:opacity-70 transition-opacity duration-300"></div>
                <div className="absolute inset-0 flex items-end justify-center p-4">
                    <div className="text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <p className="text-white text-lg font-bold mb-1">
                            {text}
                        </p>
                        {label && (
                            <p className="text-white text-sm font-medium">
                                {label}
                            </p>
                        )}
                    </div>
                </div>
            </div>
            <EquipmentModal
                isOpen={showModal}
                onClose={() => setShowModal(false)}
                equipment={{
                    imageUrl: Array.isArray(imageUrl) ? imageUrl : [imageUrl],
                    text: text,
                    label: label,
                    PerformanceHighlights: PerformanceHighlights,
                    relatedProjects: relatedProjects,
                }}
            />
        </>

    );
};

export default EquipmentCard;
