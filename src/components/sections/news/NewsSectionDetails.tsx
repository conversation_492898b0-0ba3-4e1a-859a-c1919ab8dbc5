import React from 'react';
import { Calendar, Share2 } from 'lucide-react';
import { News } from '@/types';
import { news } from '@/messages/news';
import Link from 'next/link';
import ShareButton from './ShareButton';

const NewsSection = ({
    title,
    date,
    description,
}: News) => {

    return (
        <section className="py-8 sm:py-12 md:py-16 px-4 md:px-6 lg:px-8">
            <div className="container mx-auto">
                {/* Article Header */}
                <div className="w-full mb-6">
                    <div className="flex justify-between items-start w-full">
                        <h1 className="text-base me-2 md:me-0 md:text-3xl font-semibold md:font-bold text-[#365D92] mb-4">
                            {title}
                        </h1>
                        <ShareButton />
                    </div>

                    <div className="flex items-center text-gray-500 mb-6">
                        <Calendar size={16} className="me-2" />
                        <span>
                            {date.toLocaleDateString('en-US', {
                                weekday: 'short',
                                day: 'numeric',
                                month: 'short',
                                year: 'numeric',
                            })}
                        </span>
                    </div>
                </div>
                <div className="flex flex-col lg:flex-row gap-8">
                    {/* Main Article */}
                    <div className="flex-1 lg:max-w-4xl">
                        {/* Article Content */}
                        <div className="prose prose-lg max-w-none">
                            <div className="border-s-4 border-[#365D92] ps-6 mb-6">
                                <p className="text-black leading-relaxed text-base font-semibold">
                                    {description?.split('\n\n')[0]?.trim() || 'No description available'}
                                </p>
                            </div>

                            <p className="text-neutral-700 leading-relaxed mb-6">
                                {description?.split('\n\n')[1]?.trim() || ''}
                            </p>

                            <p className="text-neutral-700 leading-relaxed">
                                {description?.split('\n\n')[2]?.trim() || ''}
                            </p>
                        </div>
                    </div>

                    {/* Sidebar - More Articles */}
                    <aside className="lg:w-80 lg:flex-shrink-0 mt-10">
                        <div className="lg:sticky lg:top-8">
                            <h2 className="text-2xl text-black font-semibold mb-6">More articles</h2>

                            <div className="space-y-6">
                                {news.slice(0, 3).map((article) => (
                                    <Link href={`/news/${article.id}`} key={article.id}>
                                        <div className="flex gap-5 group cursor-pointer mb-5">
                                            <div className="flex-shrink-0">
                                                <img
                                                    src={article.image[0]}
                                                    alt={article.title}
                                                    className="w-32 h-28 object-cover rounded-[2.89px] shadow-[0px_1.44516122341156px_20.449031829833984px_0px_rgba(0,0,0,0.08)] group-hover:opacity-80 transition-opacity duration-200"
                                                />
                                            </div>

                                            <div className="flex-1 min-w-0">
                                                <h3 className="text-sm font-medium  text-black group-hover:text-[#365D92] transition-colors duration-200 mb-1">
                                                    {article.title}
                                                </h3>

                                                <div className="flex items-center text-[10px] text-gray-500 mb-2">
                                                    <Calendar size={12} className="me-1" />
                                                    <span>{article.date.toLocaleDateString()}</span>
                                                </div>

                                                <p className="text-xs text-zinc-500 leading-relaxed line-clamp-2">
                                                    {article.subDescription}
                                                </p>
                                            </div>
                                        </div>
                                    </Link>
                                ))}
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </section>

    );
};

export default NewsSection;