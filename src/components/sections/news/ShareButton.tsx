'use client'

import Link from 'next/link';
import Image from 'next/image';
import React, { useState } from 'react';
import { socialIcons } from '@/messages/news';

const ShareButton = () => {
    const [isHovered, setIsHovered] = useState(false);

    return (
        <div >
            <div className="relative pt-4">
                <div
                    className={`
            relative bg-white border-2 border-[#365D92] 
            transition-all duration-300 ease-in-out cursor-pointer
            shadow-md hover:shadow-lg
            flex items-center justify-center w-28 h-10 sm:w-40 sm:h-12 rounded-xl
            ${isHovered
                            ? 'pt-6 pb-2 px-4 sm:pt-8 sm:pb-4 sm:px-6'
                            : 'px-6 py-3 sm:px-8 sm:py-4'
                        }`}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                >
                    {/* Share Text / Label */}
                    <div
                        className={`absolute left-1/2 transform -translate-x-1/2 font-semibold transition-all duration-300 ease-in-out z-10
                        ${isHovered
                                ? 'top-0 -translate-y-1/2 bg-[#365D92] text-white px-4 py-1 rounded-full text-sm sm:text-base shadow-sm'
                                : 'top-1/2 -translate-y-1/2 text-[#365D92] text-lg sm:text-xl'
                            }
                    `}>Share
                    </div>

                    {/* Social Icons*/}
                    <div
                        className={`flex items-center justify-center w-full gap-3 transition-opacity duration-300 ease-in-out
                        ${isHovered
                                ? 'opacity-100 visible'
                                : 'opacity-0 invisible h-0'
                            }
            `}
                    >
                        {socialIcons.map((social, index) => (
                            <Link href="/" target="_blank"
                                key={index}
                                className=" text-[#365D92] transition-transform duration-200 hover:scale-150 focus:outline-none">
                                <Image
                                    aria-label={`Share on ${social.label}`}
                                    src={social.src}
                                    alt={`${social.label} Icon`}
                                    className="md:w-full md:h-full object-cover"
                                    width={30}
                                    height={30}
                                />
                            </Link>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ShareButton;
