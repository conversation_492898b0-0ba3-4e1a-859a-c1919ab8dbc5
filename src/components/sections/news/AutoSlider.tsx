'use client'

import { AutoSliderImages } from '@/data/news';
import { SliderProps } from '@/types';
import React, { useState, useEffect } from 'react';



const AutoSlider = ({
    images = AutoSliderImages, //default images if there are no images
    interval = 5000
}: SliderProps) => {

    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        const slideInterval = setInterval(() => {
            setCurrentIndex(prev => (prev + 1) % images.length);
        }, interval);

        return () => {
            clearInterval(slideInterval);
        };
    }, [images.length, interval]);

    // Calculate progress based on current image position
    const progressPercentage = ((currentIndex + 1) / images.length) * 100;

    return (
        <div className="relative w-[80%] lg:w-full max-w-6xl mx-auto mt-28">
            {/* Image Container */}
            <div className="relative h-64 md:h-80 lg:h-[504px] overflow-hidden rounded-lg shadow-lg">
                {images.map((image, index) => (
                    <div
                        key={index}
                        className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${index === currentIndex ? 'opacity-100' : 'opacity-0'
                            }`}
                    >
                        <img
                            src={image}
                            alt={`Slide ${index + 1}`}
                            className="w-full h-full object-cover"
                        />
                    </div>
                ))}

                {/* Overlay gradient for better text visibility if needed */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none" />
            </div>

            {/* Progress Bar */}
            <div className="absolute -bottom-5 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full px-4 z-10">
                <div className="w-[10%] mx-auto bg-gray-200 rounded-full h-1 overflow-hidden">
                    <div
                        className="h-full bg-[#365D92] rounded-full transition-all duration-500 ease-in-out"
                        style={{ width: `${progressPercentage}%` }}
                    />
                </div>
            </div>


        </div>
    );
};

export default AutoSlider;