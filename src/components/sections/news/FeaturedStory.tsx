import { FeaturedStoryProp } from '@/types';
import { CalendarDays } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

const FeaturedStory = ({ image, title, date, description }: FeaturedStoryProp) => {
    return (
        <section className="py-8 sm:py-12 px-4 md:px-6 lg:px-8  mt-15 md:mt-0">
            <div className='container mx-auto max-w-[830px] flex flex-col justify-center items-center'>
                {/* Title directly above the image */}
                <h3 className="self-start ps-0 lg:ps-33 text-sm md:text-2xl font-semibold text-black mb-4">
                    Project Description
                </h3>

                {/* Image Container */}
                <div
                    className="w-full max-w-[830px] aspect-[830/514] relative bg-gradient-to-b from-black/0 to-neutral-800/50 rounded-lg shadow-[0px_4px_56.6px_0px_rgba(0,0,0,0.08)] overflow-hidden cursor-pointer"
                    style={{
                        backgroundImage: `url(${image})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                    }}
                >

                    {/* Icon and label */}
                    <div className="absolute right-[5%] top-0 z-10">
                        <Image
                            src="/images/newsIcon.svg"
                            alt="about-icon"
                            width={30}
                            height={30}
                            priority
                            className='w-[40px] h-[40px] sm:w-[80px] sm:h-[80px]'
                        />
                        <p className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-xs md:text-base font-normal font-['Pridi'] pb-4">
                            Event
                        </p>
                    </div>

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-0" />

                    {/* Content */}
                    <div className="relative z-10 flex flex-col justify-end h-full w-full p-6 space-y-4">
                        <div className="flex flex-col gap-1">
                            <div className="text-[#FFFFFF] text-md md:text-xl font-semibold">
                                {title}
                            </div>
                            <div className="inline-flex items-center gap-1 text-white text-xs font-medium">
                                <CalendarDays size={18} />
                                <span>{date}</span>
                            </div>
                        </div>
                        <div className="text-white text-xs md:text-sm font-normal max-w-xl ">
                            {description}
                            <Link href='/' className='md:text-base font-medium underline'>
                                Read More
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default FeaturedStory;
