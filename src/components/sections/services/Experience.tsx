import React from 'react'
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

const Experience = () => {
    return (
        <section className="w-full py-16 overflow-hidden">
            <div className="container mx-auto px-4 md:px-6 lg:px-8 relative z-10"   >
                <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                    {/* Left Column - Text Content */}
                    <div className="md:w-1/2 space-y-6 text-center md:text-start">
                        <div className="space-y-2 pb-3 text-2xl sm:text-3xl md:text-4xl font-medium md:text-start text-gray-800">
                            <h2 className='text-2xl md:text-3xl font-medium'>Client experience</h2>
                        </div>

                        <div className="text-gray-700 space-y-4 pb-7">
                            <p>
                                Lorem ipsum dolor sit amet consectetur. I<PERSON>ulis nisi eu massa pretium eget.Lorem ipsum dolor sit amet consectetur. Iaculis nisi Lorem ipsum dolor sit amet consectetur. Iaculis nisi eu massa pretium eget.Lorem ipsum dolor sit amet consectetur. Iaculis nisi Lorem ipsum dolor sit amet consectetur. Iaculis nisi eu massa pretium eget.Lorem ipsum dolor sit amet consectetur. Iaculis nisi .
                            </p>
                        </div>

                        <Link href="/" className="inline-block">
                            <Button variant="primary" hasArrow={true} className='!w-56 !text-sm'>
                                Schedule a Consultation
                            </Button>
                        </Link>
                        <div className="flex justify-start mb-4">
                            <button className='text-base font-normal underline text-[#365D92]'>
                                View Our Portfolio
                            </button>
                        </div>
                    </div>

                    {/* Right Column  */}
                    <div className="hidden md:flex md:w-1/2 md:flex-col justify-center gap-4 items-center px-4 md:px-0 relative">
                        {/* Main Image */}
                        <div className="w-96 h-96 md:ms-[5%] mx-auto md:mx-0 rounded-2xl ">
                            <Image
                                src="/images/experienceImg.jpg"
                                alt="experience Img"
                                width={560}
                                height={400}
                                className="md:w-full md:h-96 w-56 h-44 rounded-[10px] shadow-[0px_4px_21.200000762939453px_0px_rgba(0,0,0,0.15)] object-cover"
                            />
                        </div>

                        {/* First Fact Box */}
                        <div className="absolute top-10 -right-25 w-56  bg-blue-100/20 rounded-md  outline-[0.40px] outline-offset-[-0.40px] outline-stone-300 backdrop-blur-md  p-4 md:p-6 shadow-md  overflow-hidden inline-flex flex-col justify-center items-start gap-4">
                            <div className="flex justify-baseline gap-2 text-[#365D92] ">
                                <span className="text-2xl md:text-5xl font-medium ">73</span>
                                <div className="flex items-center ">
                                    <span className="me-2">+</span>
                                    <span className="text-sm">Fact Title</span>
                                </div>
                            </div>
                            <p className="text-[10.38px] text-black">
                                Lorem ipsum dolor sit amet consectetur. Iaculis nisi eu massa pretium eget.Lorem ipsum dolor sit amet consectetur. Iaculis nisi
                            </p>
                        </div>

                        {/* Second Fact Box */}
                        <div className="w-56 absolute -bottom-10 right-2 bg-blue-100/20 rounded-md  outline-[0.40px] outline-offset-[-0.40px] outline-stone-300 backdrop-blur-md  p-4 md:p-6 shadow-md  overflow-hidden inline-flex flex-col justify-center items-start gap-4">
                            <div className="flex justify-baseline gap-2 text-[#365D92] ">
                                <span className="text-2xl md:text-5xl font-medium ">73</span>
                                <div className="flex items-center ">
                                    <span className="me-2">+</span>
                                    <span className="text-sm">Fact Title</span>
                                </div>
                            </div>
                            <p className="text-[10.38px] text-black">
                                Lorem ipsum dolor sit amet consectetur. Iaculis nisi eu massa pretium eget.Lorem ipsum dolor sit amet consectetur. Iaculis nisi
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Experience