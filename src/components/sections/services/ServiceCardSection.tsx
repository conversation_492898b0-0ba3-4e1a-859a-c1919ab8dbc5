import { cardContents } from "@/messages/services";
import Card from "./ServiceCard";

export default function CardGrid() {

    return (
        <section className="pb-8 sm:pb-12 md:pb-16">
            <div className=" mx-auto px-4">
                <div className="flex flex-wrap gap-4 text-sm md:text-base justify-center text-start">
                    {cardContents.map((text, index) => (
                        <Card key={index} text={text} />
                    ))}
                </div>
            </div>
        </section>
    );
}