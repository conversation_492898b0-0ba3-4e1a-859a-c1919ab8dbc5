"use client"

import { useState, useEffect, useRef } from 'react';
import { ServicesSectionProps } from '@/types';
import Image from 'next/image';
import Link from 'next/link';


const ServicesListDetailsSectionSlider = ({ services }: ServicesSectionProps) => {
    // State for active service
    const [activeService, setActiveService] = useState<string>(services[0]?.id || '');
    const [animatingOut, setAnimatingOut] = useState<string | null>(null);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    const handleServiceClick = (serviceId: string) => {
        if (serviceId === activeService || animatingOut) return;

        // Set the current active service as animating out
        setAnimatingOut(activeService);

        // Clear any existing timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        // After animation completes, set the new active service
        timeoutRef.current = setTimeout(() => {
            setActiveService(serviceId);
            setAnimatingOut(null);
        }, 600);
    };

    // Clean up timeout on unmount
    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    return (
        <section className="pb-8 sm:pb-12 md:pb-16 " style={{
            backgroundImage: 'url(/images/servDetailsBackground.svg)',
            backgroundPosition: 'left top',
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
            minHeight: '100%',
            width: '100%',
            zIndex: 1
        }}>
            <div className="container mx-auto px-4">
                {/* For mobile: Stack layout vertically */}
                <div className="flex flex-col md:hidden">
                    {/* Services List - Top on mobile */}
                    <div className="w-full text-[#CFCFCF] bg-gradient-to-b from-[#4571A7] to-[#30527D] rounded-lg flex flex-col justify-center items-start mb-6">
                        {services.map((service) => (
                            <div
                                key={service.id}
                                onClick={() => handleServiceClick(service.id)}
                                className={`w-full cursor-pointer transition-all duration-300 py-4 ps-10 border-s-4 flex flex-row gap-2 items-center hover:text-white ${activeService === service.id
                                    ? 'border-s-[#39A171] text-white'
                                    : 'border-s-transparent'
                                    }`}
                            >
                                <span className="opacity-70 mb-1 text-[10px] font-medium font-['Inter'] me-8">{service.number}</span>
                                <h3 className="text-lg font-medium">{service.title}</h3>
                            </div>
                        ))}
                    </div>

                    {/* Active Service Content - Below the list on mobile */}
                    <div className="w-full">
                        {services.map((service) => {
                            if (service.id !== activeService) return null;

                            return (
                                <div key={service.id} >
                                    <div className="mb-8">
                                        <h2 className="text-xl mb-3 text-Black font-medium">{service.heading}</h2>
                                        <p className="text-xs text-[#666666] leading-relaxed mb-8 font-normal px-10">{service.description}</p>
                                        <a
                                            href="#"
                                            className="inline-block text-[#365D92] font-medium relative group text-sm"
                                        >
                                            More about {service.title.toLowerCase()}
                                            <span className="absolute left-0 bottom-0 w-0 h-0.5 bg-[#365D92] group-hover:w-full transition-all duration-300"></span>
                                        </a>
                                    </div>

                                    <div className="w-full relative h-[300px] shadow-[0px_1.5803102254867554px_22.361391067504883px_0px_rgba(0,0,0,0.08)]">
                                        <Image
                                            src={service.imageUrl}
                                            alt={service.imageAlt}
                                            fill
                                            className="object-cover rounded-lg"
                                        />
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>

                <div className="hidden md:flex flex-row overflow-hidden h-auto">
                    {/* Services List - Left Side */}
                    <div className="md:w-75 text-[#CFCFCF] bg-gradient-to-b from-[#4571A7] to-[#30527D] rounded-lg flex flex-col justify-center items-start">
                        {services.map((service) => (
                            <div
                                key={service.id}
                                onClick={() => handleServiceClick(service.id)}
                                className={`relative cursor-pointer transition-all duration-300 py-3 ps-10 border-s-4 flex flex-row gap-2 justify-baseline items-center hover:text-white ${activeService === service.id
                                    ? 'border-s-[#39A171] text-white'
                                    : 'border-s-transparent'
                                    }`}
                            >
                                <span className="block opacity-70 mb-1 text-[10px] font-medium font-['Inter'] me-8">{service.number}</span>
                                <h3 className="text-lg font-medium">{service.title}</h3>
                            </div>
                        ))}
                    </div>

                    {/* Service Content - Right Side */}
                    <div className="relative flex-1 overflow-hidden h-[445px] my-10">
                        {services.map((service) => {
                            const isActive = service.id === activeService;
                            const isAnimatingOut = service.id === animatingOut;

                            let transformClass = 'translate-x-full opacity-0';
                            if (isActive) transformClass = 'translate-x-0 opacity-100';
                            if (isAnimatingOut) transformClass = '-translate-x-full opacity-0';

                            return (
                                <div
                                    key={service.id}
                                    className={`absolute inset-0 p-10 transition-all duration-600 ease-in-out flex flex-row justify-between ${transformClass} ${!isActive && !isAnimatingOut ? 'pointer-events-none' : ''
                                        }`}
                                >
                                    <div className="w-1/2">
                                        <h2 className="text-xl mb-3 text-Black font-medium">{service.heading}</h2>
                                        <p className="text-xs text-[#666666] leading-relaxed mb-8 font-normal px-10">{service.description}</p>
                                        <Link
                                            href="#"
                                            className="inline-flex items-center gap-2 text-[#365D92] font-medium relative group text-sm hover:translate-x-2 transition-all duration-300"
                                        >
                                            <Image
                                                src="/images/left-short-arrow.svg"
                                                alt={service.imageAlt}
                                                width={10}
                                                height={10}
                                                className="opacity-0 group-hover:opacity-100 transition-all duration-300"
                                            />
                                            More about {service.title.toLowerCase()}
                                        </Link>
                                    </div>

                                    <div className="w-1/2 relative h-80 shadow-[0px_1.5803102254867554px_22.361391067504883px_0px_rgba(0,0,0,0.08)]">
                                        <Image
                                            src={service.imageUrl}
                                            alt={service.imageAlt}
                                            fill
                                            className="object-cover rounded-lg"
                                        />
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default ServicesListDetailsSectionSlider;