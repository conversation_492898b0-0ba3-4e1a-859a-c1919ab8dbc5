"use client"

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { images } from '@/messages/services';

export default function ImageSlider() {

    const [activeIndex, setActiveIndex] = useState(2);

    const goToPrevious = () => {
        setActiveIndex(prev => (prev === 0 ? images.length - 1 : prev - 1));
    };

    const goToNext = () => {
        setActiveIndex(prev => (prev === images.length - 1 ? 0 : prev + 1));
    };

    useEffect(() => {
        const interval = setInterval(goToNext, 5000);
        return () => clearInterval(interval);
    }, []);

    return (
        <section className="py-8 sm:py-12 md:py-10 px-4 md:px-6 lg:px-8" style={{
            backgroundImage: 'url(/images/servDetailsBackground1.svg)',
            backgroundPosition: 'right top',
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
            minHeight: '100%',
            width: '100%',
            zIndex: 1
        }}>
            <div className="relative w-full  h-96 overflow-hidden mx-auto">
                <div className="flex items-center justify-center w-full h-full">
                    {images.map((image, index) => {
                        // Calculate relative position from active index
                        const position = (index - activeIndex);
                        // Normalize for circular behavior
                        const normalizedPosition = position < -2
                            ? position + images.length
                            : position > 2
                                ? position - images.length
                                : position;

                        // Apply different styling based on position
                        const getStyles = () => {
                            switch (normalizedPosition) {
                                case -2: return "absolute transform -translate-x-90 scale-60 z-10";
                                case -1: return "absolute transform -translate-x-50 scale-80 z-20";
                                case 0: return "absolute transform translate-x-0 scale-100 z-30";
                                case 1: return "absolute transform translate-x-50 scale-80 z-20";
                                case 2: return "absolute transform translate-x-90 scale-60 z-10";
                                default: return "hidden";
                            }
                        };

                        return (
                            <div
                                key={index}
                                className={`transition-all duration-500 ease-in-out rounded-xl " ${getStyles()}`}
                            >
                                <img
                                    src={image.src}
                                    alt={image.alt}
                                    // TODO: Fix the shadow here 
                                    className="w-[684.44px] object-cover rounded-xl shadow-2xs"
                                />
                            </div>
                        );
                    })}
                </div>

                {/* Navigation buttons */}
                <button
                    onClick={goToPrevious}
                    className="absolute -left-1 top-1/2 transform -translate-y-1/2  rounded-full p-2 z-40 hover:cursor-pointer transition-colors"
                    aria-label="Previous slide"
                >
                    <Image
                        src='/images/serviceSliderIcon.svg'
                        alt={'serviceSlider Icon'}
                        className="md:w-full md:h-full object-cover"
                        width={20}
                        height={20} />
                </button>
                <button
                    onClick={goToNext}
                    className="absolute -right-1 top-1/2 transform -translate-y-1/2  rounded-full p-2 z-40 hover:cursor-pointer transition-colors"
                    aria-label="Next slide"
                >
                    <Image
                        src='/images/serviceSliderIcon.svg'
                        alt={'serviceSlider Icon'}
                        className="md:w-full md:h-full object-cover rotate-180"
                        width={20}
                        height={20} />
                </button>
            </div>
        </section>

    );
}