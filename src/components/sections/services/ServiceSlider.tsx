"use client"

import { Button } from '@/components/ui/Button';
import { slides } from '@/data/services';
import Link from 'next/link';
import { useState, useCallback } from 'react';

const ServiceSlider = () => {
    // State to track which slide is currently expanded
    const [expandedId, setExpandedId] = useState<number>(1);

    const handleMouseEnter = useCallback((id: number) => {
        setExpandedId(id);
    }, []);

    return (
        <section className='pt-8 sm:py-12 md:py-10 px-4 md:px-6 lg:px-8 mx-auto container mb-[4%]'>
            <div className="flex flex-col justify-center w-full rounded-xl gap-2 md:pb-0 hide-scrollbar">
                {slides.map((slide) => {
                    const isExpanded = expandedId === slide.id;

                    return (
                        <div
                            key={slide.id}
                            className={`relative rounded-3xl overflow-hidden cursor-pointer transition-all duration-500 ease-in-out
                                ${isExpanded ? 'h-96' : 'h-56'}`}
                            onMouseEnter={() => handleMouseEnter(slide.id)}
                            role="button"
                            tabIndex={0}
                            aria-expanded={isExpanded}
                            aria-label={`${slide.title} project. ${isExpanded ? 'Currently expanded' : 'Click to expand'}`}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    handleMouseEnter(slide.id);
                                }
                            }}
                        >
                            <div
                                className="absolute inset-0 bg-cover bg-center bg-black/60 before:content-[''] before:absolute before:inset-0 before:bg-gradient-to-b before:from-black/80 before:to-transparent"
                                style={{ backgroundImage: `url(${slide.image})` }}
                                aria-hidden="true"
                            />

                            <div className='absolute top-4 left-6 text-white font-["Pridi"] animate-fadeIn'>
                                <p className='hidden md:!block text-4xl font-medium animate-fadeIn'>{`{${slide.id}}`}</p>
                            </div>

                            <div className={`text-white font-["Pridi"] animate-fadeIn absolute ${isExpanded ? 'top-8 left-1/2 transform -translate-x-1/2' : 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'}`}>
                                <h3 className="text-2xl md:text-5xl text-center font-normal">
                                    {slide.title}
                                </h3>
                            </div>

                            {isExpanded && (
                                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-5/6 h-3/6 md:h-[498px] flex flex-col items-center justify-center animate-fadeIn p-4 text-white">
                                    <div className="flex flex-col items-center w-full mt-8">
                                        <p className="w-full text-center text-Black text-sm lg:text-xl font-normal font-['Pridi'] md:line-clamp-none line-clamp-2">
                                            {slide.description}
                                        </p>
                                        <Link
                                            href={`/services/${slide.id}`}
                                        >
                                            <Button
                                                className="!rounded-full shadow-[0px_4px_23.200000762939453px_0px_rgba(0,0,0,0.25)] mt-9 text-black" variant="secondary" hasArrow={true}>See Details</Button>
                                        </Link>
                                    </div>
                                </div>
                            )}
                        </div>
                    );
                })}
            </div>
        </section>
    )
}

export default ServiceSlider