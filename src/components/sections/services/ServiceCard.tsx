import { ServiceCardProps } from '@/types';
import Image from 'next/image';


const Card = ({ text }: ServiceCardProps) => {
    return (
        <div className="rounded-lg w-56 " style={{
            background: 'linear-gradient(186deg, #54AD84 4.43%, #6798D4 134.03%)',
            padding: '1px'
        }}>
            <div className="bg-white rounded-lg p-6 h-full">
                <div className="flex flex-col items-start mb-4">
                    <Image
                        src='/images/serviceDetailsCardIcon.svg'
                        alt={'serviceDetailsCard Icon'}
                        className='w-16 h-16'
                        width={70}
                        height={70}
                    />
                </div>
                <p className="text-Black text-base font-normal">{text}</p>
            </div>
        </div>
    );
};

export default Card;