import MemberCard from "@/components/ui/MemberCard";
import { memberData } from "@/messages/about";


const BoardMembers = () => {

    return (
        <section className=" px-4 md:px-6 lg:px-8 relative bg-no-repeat"
            style={{
                backgroundImage: 'url(/images/members-background.svg)',
                backgroundPosition: 'center',
                backgroundSize: 'contain',
                backgroundRepeat: 'repeat',
                minHeight: '100%',
                width: '100%'
            }}>
            <div className="container mx-auto">
                <div className="text-center mb-12">
                    <h2 className="text-center text-4xl font-medium text-black mb-4"><span className="text-[#39A171]">Board</span> Members</h2>
                    <p className="text-base text-gray-600 max-w-3xl mx-auto">
                        Meet the experts shaping our future. Our board members bring strategic insights and leadership to ensure continued progress and industry excellence.
                    </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-5 md:mb-10 lg:mb-20">
                    {memberData.map((member) => (
                        <MemberCard
                            key={member.id}
                            imageUrl={member.imageUrl}
                            name={member.name}
                            title={member.title}
                            about={member.about}
                            achievements={member.achievements}
                            links={member.links}
                        />
                    ))}
                </div>

            </div>
        </section>
    );
};

export default BoardMembers;

// <MemberCard
//     key={data.id}
//     imageUrl={data.imageUrl}
//     name={data.name}
//     title={data.title}
// />
