import Accordion from "@/components/ui/Accordion"
import { governanceItems } from "@/data/about"


const Governance = () => {
    return (
        <section className="py-8 sm:py-12 md:py-10 px-4 md:px-6 lg:px-8 relative bg-no-repeat"
            style={{
                backgroundImage: 'url(/images/governanceBackground.svg)',
                backgroundPosition: 'center',
                backgroundSize: 'cover',
                backgroundRepeat: 'repeat',
            }}>
            <div className="container mx-auto text-center">
                {/* Header Section */}
                <div className="mb-14">
                    <h2 className="text-2xl sm:text-3xl md:text-4xl font-semibold text-[#30527d] mb-2 sm:mb-4 break-words leading-tight sm:leading-normal">
                        governance
                    </h2>
                    <p className="text-base text-[#6A849F] max-w-3xl mx-auto">
                        Ensuring transparency, accountability, and ethical leadership. Explore our governance framework, key certifications, and investor relations, demonstrating our commitment to the highest standards of corporate integrity.
                    </p>
                </div>

                {/* Accordion Section */}
                <div className="space-y-2">
                    {governanceItems.map((item, index) => (
                        <Accordion
                            key={`governance-${index}`}
                            title={item.title}
                        >
                            <p className="text-start text-[#195639] text-base font-normal font-['Titillium_Web'] underline leading-normal">
                                {item.content}
                            </p>
                        </Accordion>
                    ))}
                </div>
            </div>
        </section>
    )
}

export default Governance