"use client"

import { useRef, useEffect, useState } from 'react';
import { XIcon } from 'lucide-react';
import Image from 'next/image';
import { MemberModalProps } from '@/types';
import Link from 'next/link';

const MemberModal = ({ isOpen, onClose, member }: MemberModalProps) => {
    const modalRef = useRef<HTMLDivElement>(null);
    const [isVisible, setIsVisible] = useState(false);

    // Add animation effects
    useEffect(() => {
        if (isOpen) {
            // Small delay to ensure smooth animation
            const timer = setTimeout(() => {
                setIsVisible(true);
            }, 50);
            return () => clearTimeout(timer);
        } else {
            setIsVisible(false);
        }
    }, [isOpen]);

    // Close modal when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        // Add event listener if modal is open
        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        // Clean up event listener
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);



    return (
        <div
            className={`fixed inset-0 z-50 flex items-center justify-center transition-opacity duration-300 ${isOpen ? 'pointer-events-auto' : 'pointer-events-none'} ${isVisible ? 'bg-[#2222226e] bg-opacity-30' : 'bg-opacity-0'}`}
            onClick={onClose}
            style={{ opacity: isVisible ? 1 : 0 }}
        >
            <div
                ref={modalRef}
                className="bg-white shadow-xl rounded-2xl backdrop-blur-[19.15px] overflow-hidden transform transition-all duration-300 flex
                        w-[80vw] h-[80vh] max-w-[921px] max-h-[635px] md:w-[921px] md:h-[635px]"
                style={{
                    transform: isVisible ? 'scale(1)' : 'scale(0.95)',
                    opacity: isVisible ? 1 : 0
                }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Left side - Image */}
                <div className="w-1/2 relative">
                    <Image
                        src={member.imageUrl}
                        alt={member.name}
                        fill
                        className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-neutral-700/70"></div>
                    <div className="absolute bottom-0 flex flex-col text-center right-0 p-6 text-white">
                        <h3 className="text-white text-xs font-bold">{member.name}</h3>
                        <p className="text-white/80 text-xs">{member.title}</p>
                    </div>
                </div>

                {/* Right side - Content */}
                <div className="w-1/2 flex flex-col">
                    <div className="flex justify-end p-4">
                        <button
                            onClick={onClose}
                            className="p-1 rounded-full hover:cursor-pointer transition-colors"
                        >
                            <XIcon size={20} />
                        </button>
                    </div>

                    <div className="p-6 overflow-y-auto flex-1">
                        <div className="space-y-15">
                            <div>
                                <h4 className="mb-3 text-Black text-base font-medium">About</h4>
                                <div className="text-[#666666] text-xs font-normal relative">
                                    <span className="relative inline-block max-w-full">
                                        <span className="hidden sm:inline">
                                            {member.about || "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam."}
                                        </span>
                                        <span className="sm:hidden">
                                            {member.about?.slice(0, 40) || "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing."}
                                        </span>
                                    </span>
                                    <div className="absolute bottom-0 right-0 w-20 h-full bg-gradient-to-l from-white to-transparent"></div>
                                </div>
                            </div>

                            <div>
                                <h4 className="mb-3 text-Black text-base font-medium">Key Achievements</h4>
                                <div className="text-[#666666] text-xs font-normal relative">
                                    <span className="relative inline-block max-w-full">
                                        <span className="hidden sm:inline">
                                            {member.achievements || "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam."}
                                        </span>
                                        <span className="sm:hidden">
                                            {member.achievements?.slice(0, 40) || "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing."}
                                        </span>
                                    </span>
                                    <div className="absolute bottom-0 right-0 w-20 h-full bg-gradient-to-l from-white to-transparent"></div>
                                </div>
                            </div>

                            <div>
                                <h4 className="mb-3 text-Black text-base font-medium">Links</h4>
                                <div className="flex flex-wrap gap-6 text-[#666666] text-xs font-normal cursor-pointer">
                                    {member.links?.youtube && (
                                        <Link href={'member.links.youtube'} target="_blank" className=" hover:underline">
                                            YouTube
                                        </Link>
                                    )}
                                    {member.links?.linkedin && (
                                        <Link href={member.links.linkedin} target="_blank" className=" hover:underline">
                                            LinkedIn
                                        </Link>
                                    )}
                                    {member.links?.bio && (
                                        <Link href={member.links.bio} target="_blank" className=" hover:underline">
                                            Bio
                                        </Link>
                                    )}
                                    {(!member.links?.youtube && !member.links?.linkedin && !member.links?.bio) && (
                                        <>
                                            <span className="hover:underline">YouTube</span>
                                            <span className="hover:underline">LinkedIn</span>
                                            <span className="hover:underline">Bio</span>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MemberModal;