import { Button } from '@/components/ui/Button';
import Image from 'next/image';


const Information = () => {
    return (
        <section className="py-8 sm:py-12 md:py-16 px-4 md:px-6 lg:px-8 mt-[15%] md:mt-0">
            <div className="container mx-auto relative">
                {/* Main flex container with 1240px breakpoint */}
                <div className="flex flex-col items-center [@media(min-width:1240px)]:flex-row [@media(min-width:1240px)]:justify-around gap-8">
                    {/* Left Column - Text Content */}
                    <div className="w-full max-w-2xl text-center [@media(min-width:1240px)]:text-start">
                        <div className="flex flex-col items-center [@media(min-width:1240px)]:items-start gap-4 mb-12">
                            <div className='flex flex-row gap-3 items-center'>
                                <Image
                                    src="/images/about-icon.svg"
                                    alt="about-icon"
                                    width={30}
                                    height={30}
                                    priority
                                    className='md:w-10 md:h-10'
                                />
                                <h2 className="text-md md:text-2xl font-semibold">Who We Are</h2>
                            </div>
                            <p className="text-sm md:text-base text-[#666666] mb-4">
                                Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu.Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh. vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing ..
                            </p>
                            <Button variant='outlineGradient'>
                                <div className="relative inline-flex items-center">
                                    <Image
                                        src="/images/ic_round-download.svg"
                                        alt="Download icon"
                                        width={20}
                                        height={20}
                                        priority
                                        className="md:w-8 md:h-8 me-2 opacity-100 group-hover:opacity-0 transition-opacity duration-300"
                                    />
                                    <Image
                                        src="/images/ic_round-download-white.svg"
                                        alt="Download icon"
                                        width={20}
                                        height={20}
                                        priority
                                        className="absolute left-0 md:w-8 md:h-8 me-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                                    />
                                    Download Namaa Portfolio
                                </div>
                            </Button>
                        </div>
                        <div className="flex flex-col items-center [@media(min-width:1240px)]:items-start gap-4 mb-6">
                            <div className='flex flex-row gap-3 items-center'>
                                <Image
                                    src="/images/about-icon-2.svg"
                                    alt="about-icon"
                                    width={30}
                                    height={30}
                                    priority
                                    className='md:w-10 md:h-10'
                                />
                                <h2 className="text-md  md:text-2xl  font-semibold">Our Values</h2>
                            </div>
                            <p className="text-sm md:text-base text-[#666666] mb-4">
                                Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu..
                            </p>
                        </div>
                    </div>

                    {/* Right Column - Images/Video */}
                    <div className="hidden [@media(min-width:1240px)]:block w-full max-w-2xl relative -top-30">
                        {/* Video placeholder (first image) */}
                        <div className="absolute w-full h-[130%] -right-4/5  aspect-video  overflow-hidden ">
                            <Image
                                src="/images/information-background.png"
                                alt="Video thumbnail"
                                fill
                                className="object-cover rounded-3xl"
                            />
                        </div>

                        {/* Second image */}
                        <div className="relative top-13 left-30 w-lg h-80 aspect-square rounded-3xl overflow-hidden ">
                            <Image
                                src="/images/information-background-2.png"
                                alt="About our company"
                                fill
                                className="object-cover rounded-3xl border-8 border-white"
                            />
                            {/* Play button icon */}
                            <div className="absolute inset-0 flex items-center justify-center">
                            <Image
                                src="/images/videoPlayIcon.svg"
                                alt="Video thumbnail"
                                width={30}
                                height={30}
                                priority
                                className='md:w-20 md:h-20'
                            />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Information