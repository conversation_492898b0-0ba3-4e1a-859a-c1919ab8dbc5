"use client"

import CertificationCard from "@/components/ui/CertificationCard";
import { certifications } from "@/messages/about";



const CertificationsSection = () => {


    return (
        <section className="py-8 sm:py-12 px-4 md:px-6 lg:px-8">
            <div className="container mx-auto text-center">
                <div className="mb-14">
                    <h2 className="text-2xl sm:text-3xl md:text-4xl font-semibold text-black mb-2 sm:mb-4 break-words leading-tight sm:leading-normal">
                        Certified Excellence
                    </h2>
                    <p className="text-base text-[#666666] max-w-3xl mx-auto">
                        Committed to the highest industry standards, our certifications reflect our dedication to quality, safety, and environmental responsibility.
                    </p>
                </div>


                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 text-center mx-auto space-y-2">
                    {certifications.map((cert, index) => (
                        <CertificationCard
                            key={index}
                            isoNumber={cert.isoNumber}
                            date={cert.date}
                        />
                    ))}
                </div>
            </div>
        </section>
    );
};

export default CertificationsSection;