"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import SlideDots from "./SlideDots";
import { sliderData } from "@/data/homePage";


const HeroSlider = () => {
    const [currentSlide, setCurrentSlide] = useState(0);

    // Auto slide functionality
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentSlide((prev) => (prev === sliderData.length - 1 ? 0 : prev + 1));
        }, 10000);
        return () => clearInterval(interval);
    }, []);

    const nextSlide = useCallback(() => {
        setCurrentSlide((prev) => (prev === sliderData.length - 1 ? 0 : prev + 1));
    }, []);

    const prevSlide = useCallback(() => {
        setCurrentSlide((prev) => (prev === 0 ? sliderData.length - 1 : prev - 1));
    }, []);

    const goToSlide = (index: number) => {
        if (index !== currentSlide) setCurrentSlide(index);
    };

    return (
        <section className="pt-16">
            <div className="container mx-auto px-4 overflow-hidden min-h-screen">
                {/* Slides */}
                {sliderData.map((slide, index) => (
                    <div
                        key={slide.id}
                        className={`absolute top-0 left-0 w-full h-full transition-opacity duration-500 ease-in-out ${index === currentSlide ? "opacity-100 z-10" : "opacity-0 z-0"
                            }`}
                    >
                        {/* Background Image with Overlay */}
                        <div className="absolute inset-0 bg-black/50 z-10"></div>
                        <div className="absolute inset-0 z-0">
                            {/* Fallback background color while image loads */}
                            <div className="absolute inset-0 bg-gray-800"></div>

                            {/* Image with proper error handling */}
                            <Image
                                src={slide.backgroundImage}
                                loading={index === 0 ? "eager" : "lazy"}
                                alt={`Slide ${slide.id} background`}
                                fill
                                quality={90}
                                sizes="100vw"
                                className="object-cover"
                            />
                        </div>

                        {/* Content */}
                        <div className="relative z-20 container mx-auto px-4 h-full flex flex-col justify-center pt-[30px]">
                            <div className="max-w-2xl text-white">
                                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
                                    {slide.title} <br />
                                </h1>

                                <div className="border-s-4 border-[#30527d] ps-2 my-8">
                                    <p className="text-sm md:text-lg mb-4 max-w-[50%]">{slide.description}</p>
                                    <p className="text-sm md:text-lg font-semibold">{slide.projectName}</p>
                                </div>

                                <div className="flex gap-4 mt-40">
                                    <Link
                                        href="/projects"
                                    >
                                        <Button variant="secondary" hasArrow={true} >Projects</Button>
                                    </Link>
                                    <Link
                                        href="/contact-us"
                                    >
                                        <Button variant="primary" hasArrow={true} >Get in Touch</Button>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}

                {/* Navigation Dots */}
                <div className="absolute right-8 top-4/6 md:top-4/5 transform -translate-y-1/2 z-30 flex flex-col items-center">
                    <button onClick={prevSlide} aria-label="Previous slide">
                        <Image src={"images/ArrowsUp.svg"}
                            loading="lazy"
                            alt="Arrows Up img"
                            width={30}
                            height={30}
                            className="w-7 h-7 md:w-10 md:h-10 cursor-pointer"
                        />
                    </button>

                    <SlideDots
                        count={sliderData.length}
                        current={currentSlide}
                        onClick={goToSlide}
                    />

                    <button onClick={nextSlide} aria-label="Next slide">
                        <Image src={"images/ArrowsDown.svg"}
                            loading="lazy"
                            alt="Arrows Down img"
                            width={30}
                            height={30}
                            className="w-7 h-7 md:w-10 md:h-10 cursor-pointer"
                        />
                    </button>
                </div>
            </div>
        </section>
    );
};

export default HeroSlider;
