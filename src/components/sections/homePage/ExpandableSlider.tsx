"use client"

import { slides } from '@/messages/homePage';
import { useState, useCallback } from 'react';

const ExpandableSlider = () => {
    // State to track which slide is currently expanded
    const [expandedId, setExpandedId] = useState<number>(1);

    const handleMouseEnter = useCallback((id: number) => {
        setExpandedId(id);
    }, []);

    return (
        <section
            className="w-full py-16 relative bg-no-repeat"
            style={{
                backgroundImage: 'url(/images/sliderBackground.svg)',
                backgroundPosition: 'center',
                backgroundSize: 'contain',
                backgroundRepeat: 'repeat',
                minHeight: '100%',
                width: '100%'
            }}
            aria-labelledby="slider-heading"
        >
            <div className="container mx-auto px-4 md:px-6 lg:px-8">
                <div className="text-center mb-12">
                    <h2
                        id="slider-heading"
                        className="text-center text-4xl font-medium text-[#30527d] mb-4"
                    >
                        Turning Vision into Reality
                    </h2>
                    <p className="text-base text-gray-600 max-w-3xl mx-auto">
                        Every project tells a story of creativity, dedication, and impact.
                        Explore our featured work and see how we bring ideas to life.
                    </p>
                </div>

                <div className="flex flex-col md:flex-row justify-between w-full md:h-[714px] rounded-xl overflow-auto md:overflow-hidden gap-2 md:pb-0 hide-scrollbar">
                    {slides.map((slide) => {
                        const isExpanded = expandedId === slide.id;

                        return (
                            <div
                                key={slide.id}
                                className={`relative rounded-xl overflow-hidden cursor-pointer transition-all duration-500 ease-in-out 
                                    ${isExpanded ? 'md:w-4/6 h-[500px]' : 'md:w-1/6 h-24'}
                                    w-full md:h-auto mb-4 md:mb-0`}
                                onMouseEnter={() => handleMouseEnter(slide.id)}
                                onClick={() => handleMouseEnter(slide.id)} /* Added click for mobile */
                                role="button"
                                tabIndex={0}
                                aria-expanded={isExpanded}
                                aria-label={`${slide.title} project. ${isExpanded ? 'Currently expanded' : 'Click to expand'}`}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        handleMouseEnter(slide.id);
                                    }
                                }}
                            >
                                <div
                                    className="absolute inset-0 bg-cover bg-center"
                                    style={{ backgroundImage: `url(${slide.image})` }}
                                    aria-hidden="true"
                                />
                                {isExpanded && (
                                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3/6 h-3/6 md:h-[498px] bg-gradient-to-b from-zinc-300/10 to-neutral-500/0 rounded-lg border-[0.30px] border-black/20 backdrop-blur-md flex flex-col items-center justify-center animate-fadeIn p-4">
                                        <h3 className="text-center text-sm md:text-2xl font-medium mb-2 md:mb-8 text-[#30527d]">
                                            {slide.title}
                                        </h3>
                                        <div className="flex flex-col items-center w-full">
                                            <p className="w-full max-w-xs text-center text-Black text-xs md:text-sm font-normal overflow-y-auto">
                                                {slide.description}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            </div>
        </section>
    );
}

export default ExpandableSlider;