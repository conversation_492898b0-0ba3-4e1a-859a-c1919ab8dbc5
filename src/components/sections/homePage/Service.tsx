"use client"

import React, { useState, useEffect } from 'react';
import { servicesData } from '@/messages/homePage';
import { ServiceCard } from '../../ui/ServiceCard';
import { UserRoundCog } from "lucide-react";
import Image from 'next/image';

const Service = () => {
    const [currentSlide, setCurrentSlide] = useState(0);

    // Auto slide functionality
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentSlide((prev) => (prev === servicesData.length - 1 ? 0 : prev + 1));
        }, 5000);
        return () => clearInterval(interval);
    }, []);

    const nextSlide = () => {
        setCurrentSlide((prev) => (prev === servicesData.length - 1 ? 0 : prev + 1));
    };

    const prevSlide = () => {
        setCurrentSlide((prev) => (prev === 0 ? servicesData.length - 1 : prev - 1));
    };

    return (
        <section className='py-16'>
            <div className="container mx-auto px-4 md:px-6 lg:px-8">
                <div className="text-center mb-12">
                    <h2 className="text-center text-4xl font-medium text-[#30527d] mb-4">Our Service</h2>
                    <p className="text-base text-gray-600 max-w-3xl mx-auto">
                        We are committed to providing top-quality services tailored to your needs. Our expertise ensures innovative solutions that help you achieve your goals with efficiency and excellence.
                    </p>
                </div>

                {/* Desktop View */}
                <div className="hidden md:flex flex-wrap gap-6 justify-center">
                    {servicesData.map((service, index) => (
                        <div key={index} className="w-[calc(33.333%-16px)] cursor-pointer">
                            <ServiceCard
                                imageUrl={service.imageUrl}
                                title={service.title}
                                alt={service.alt}
                                icon={UserRoundCog}
                            />
                        </div>
                    ))}
                </div>

                <div className="md:hidden flex justify-end mb-4">
                    <button className='text-xs font-semibold underline text-[#30527d]'>
                        view All Services
                    </button>
                </div>

                {/* Mobile Slider View */}
                <div className="md:hidden relative flex justify-center">

                    <div className="relative w-sm h-80">
                        {servicesData.map((service, index) => (
                            <div
                                key={index}
                                className={`absolute top-0 left-0 w-full transition-opacity duration-500 ease-in-out ${index === currentSlide ? "opacity-100 z-10" : "opacity-0 z-0"
                                    }`}
                            >
                                <ServiceCard
                                    imageUrl={service.imageUrl}
                                    title={service.title}
                                    alt={service.alt}
                                    icon={UserRoundCog}
                                    className="mx-auto"
                                />
                            </div>
                        ))}
                    </div>

                    {/* Navigation Buttons */}
                    <button
                        className="absolute top-[39%] left-12 -translate-y-1/2 p-2 z-20 cursor-pointer max-[600px]:hidden"
                        onClick={prevSlide}
                        aria-label="Previous slide"
                    >
                        <Image
                            src="/images/left-icon.svg"
                            alt="left-icon for slider"
                            width={17}
                            height={17}
                            style={{ width: 'auto', height: 'auto' }}
                        />
                    </button>

                    <button
                        className="absolute top-[39%] right-12 -translate-y-1/2 p-2 z-20 cursor-pointer max-[600px]:hidden"
                        onClick={nextSlide}
                        aria-label="Next slide"
                    >
                        <Image
                            src="/images/right-icon.svg"
                            alt="right-icon for slider"
                            width={17}
                            height={17}
                            style={{ width: 'auto', height: 'auto' }}
                        />
                    </button>
                </div>
            </div>
        </section >
    );
}

export default Service;
