"use client";

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { PartnerCard } from '../../ui/PartnerCard';
import Image from "next/image";
import { cards } from '@/messages/homePage';

const GAP_SIZE = 32;

const PartnersSection = () => {
    const sliderContentRef = useRef<HTMLDivElement>(null);
    const animationRef = useRef<number | undefined>(undefined);
    // Create refs for each card to measure their width
    const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
    const [cardWidthWithGap, setCardWidthWithGap] = useState(0);

    const duplicatedCards = [...cards, ...cards, ...cards, ...cards, ...cards];

    // Function to calculate and set the card width
    const calculateCardWidth = useCallback(() => {
        if (cardRefs.current[0]) {
            const firstCardWidth = cardRefs.current[0].offsetWidth;
            setCardWidthWithGap(firstCardWidth + GAP_SIZE);
            // console.log("Calculated card width with gap:", firstCardWidth + GAP_SIZE);
        }
    }, []);

    useEffect(() => {
        // Ensure refs are populated for all duplicated cards
        cardRefs.current = cardRefs.current.slice(0, duplicatedCards.length);
        calculateCardWidth(); // Initial calculation

        window.addEventListener('resize', calculateCardWidth);
        return () => {
            window.removeEventListener('resize', calculateCardWidth);
        };
    }, [calculateCardWidth, duplicatedCards.length]);

    useEffect(() => {
        const content = sliderContentRef.current;
        // Don't start animation until width is calculated
        if (!content || cardWidthWithGap === 0) return;

        let position = 0;
        const speed = 1;

        // The reset point should be based on the original number of cards times the dynamic width
        const singleSetOfCardsWidth = cards.length * cardWidthWithGap;
        const resetPoint = -singleSetOfCardsWidth;

        const animate = () => {
            position -= speed;

            // When we've scrolled past the first set of original cards
            if (position <= resetPoint) {
                position += singleSetOfCardsWidth;
            }

            content.style.transform = `translateX(${position}px)`;
            animationRef.current = requestAnimationFrame(animate);
        };

        animationRef.current = requestAnimationFrame(animate);

        return () => {
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [cardWidthWithGap]);

    return (
        <section className="pb-12">
            <div className="mx-auto max-w-xs sm:max-w-md md:max-w-2xl text-center mb-8 sm:mb-12 container md:px-6 lg:px-8">
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-medium text-[#30527d] mb-2 sm:mb-4 break-words leading-tight sm:leading-normal">
                    Partners in Success
                </h2>
                <p className="text-base sm:text-lg text-gray-600 break-words">
                    Our journey wouldn't be the same without our incredible partners. We are proud to collaborate with industry leaders who trust us to deliver excellence and innovation
                </p>
            </div>
            <div
                className="p-6 md:p-12 overflow-hidden bg-white shadow-[0px_0px_17.0220890045166px_0px_rgba(48,82,125,0.10)]"
            >
                <div
                    className="flex gap-8 w-max"
                    ref={sliderContentRef}
                >
                    {duplicatedCards.map((card, index) => (
                        <PartnerCard
                            key={`${card.id}-${index}`}
                            className="flex-shrink-0"
                            ref={(el) => { cardRefs.current[index] = el }}
                        >
                            <div className="p-1 sm:p-4 flex items-center justify-center w-full h-full">
                                <Image
                                    src={card.imgUrl}
                                    alt={"Partner logo"}
                                    width={90}
                                    height={90}
                                    className="md:w-auto md:h-auto max-w-full max-h-full object-contain"
                                    style={{ width: 'auto', height: 'auto' }}
                                />
                            </div>
                        </PartnerCard>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default PartnersSection;

