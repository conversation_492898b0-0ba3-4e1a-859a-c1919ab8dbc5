import { SlideDotsProps } from "@/types";

const SlideDots = ({ count, current, onClick }: SlideDotsProps) => (
    <div className="flex flex-col gap-3 my-2">
        {Array(count).fill(0).map((_, i) => (
            <button
                key={i}
                onClick={() => onClick(i)}
                aria-current={i === current}
                className={`w-[6px] h-[6px] md:w-2 md:h-2 rounded-full ${i === current ? "bg-white" : "bg-gray-600"}`}
            />
        ))}
    </div>
);

export default SlideDots;