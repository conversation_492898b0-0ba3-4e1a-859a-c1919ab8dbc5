import { stats } from "@/messages/homePage";
import StatItem from "../../ui/StatItem";

const StatsSection = () => {


    return (
        <section className="pb-12">
            <div className="container mx-auto px-4">

                <div className="mx-auto max-w-xs sm:max-w-md md:max-w-2xl text-center mb-8 sm:mb-12">
                    <h2 className=" text-2xl sm:text-3xl md:text-4xl font-medium text-[#30527d] mb-2 sm:mb-4 break-words leading-tight sm:leading-normal">
                        Proven Excellence, Measured in Numbers
                    </h2>
                    <p className="text-base sm:text-lg text-gray-600 break-words">
                        Behind every statistic is a story of hard work, innovation, and commitment.
                        Our numbers showcase the milestones we've achieved and the impact we've made.
                    </p>
                </div>


                <div className="mx-auto max-w-sm sm:max-w-2xl md:max-w-6xl">
                    <div className="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 md:gap-8 lg:grid-cols-4">
                        {stats.map((stat, index) => (
                            <StatItem
                                key={index}
                                endValue={stat.value}
                                title={stat.title}
                                description={stat.description}
                                suffix={stat.suffix}
                            />
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default StatsSection;