"use client"

import { latestData, servicesData } from '@/data/homePage';
import { LatestCard } from '../../ui/LatestCard';
import { useState, useEffect } from 'react';


const Latest = () => {
    const [currentSlide, setCurrentSlide] = useState(0);
    const [isTransitioning, setIsTransitioning] = useState(false);

    useEffect(() => {
        latestData.forEach(item => {
            const img = new Image();
            img.src = item.imageUrl;
        });
    }, []);

    // Auto slide functionality with transition state
    useEffect(() => {
        if (isTransitioning) return; // Don't start a new transition if one is in progress

        const interval = setInterval(() => {
            handleSlideChange((prev) => (prev === latestData.length - 1 ? 0 : prev + 1));
        }, 5000);

        return () => clearInterval(interval);
    }, [isTransitioning]);

    const handleSlideChange = (getNextIndex: (prevIndex: number) => number) => {
        setIsTransitioning(true);
        setCurrentSlide(getNextIndex);

        // Reset transitioning state after animation completes
        setTimeout(() => {
            setIsTransitioning(false);
        }, 400); // Slightly longer than transition duration to ensure completion
    };

    return (
        <section className="py-8 sm:py-12 md:py-16">
            <div className="container mx-auto px-4">
                <div className="text-center mb-6 sm:mb-8 md:mb-12">
                    <h2 className="text-2xl sm:text-3xl md:text-4xl font-medium text-[#30527d] mb-2 sm:mb-4">Latest Updates</h2>
                    <p className="text-sm sm:text-base text-gray-600 max-w-3xl mx-auto">
                        Stay updated with the latest developments, insights, and success stories. We bring you the most relevant news to keep you informed about our journey and industry trends.
                    </p>
                </div>

                <div className="md:hidden flex justify-end mb-4">
                    <button className='text-xs font-semibold underline text-[#30527d]'>
                        view All News
                    </button>
                </div>

                {/* Desktop View */}
                <div className="hidden md:flex flex-wrap justify-center gap-4 sm:gap-6">
                    {latestData.map((item, index) => (
                        <div key={index} className="w-full md:w-[calc(50%-12px)] lg:w-[calc(33.33%-16px)] flex justify-center">
                            <LatestCard
                                image={item.imageUrl}
                                title={item.title}
                                date={item.date}
                                description={item.description}
                            />
                        </div>
                    ))}
                </div>

                {/* Mobile Slider View */}
                <div className="md:hidden relative flex justify-center">

                    <div className="relative w-sm h-80 ">
                        {latestData.map((item, index) => (
                            <div
                                key={index}
                                className={`absolute top-0 left-10 w-full  transition-opacity duration-300 ease-in-out ${index === currentSlide ? "opacity-100 z-10" : "opacity-0 z-0"
                                    }`}
                            >
                                <LatestCard
                                    image={item.imageUrl}
                                    title={item.title}
                                    date={item.date}
                                    description={item.description}
                                />
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
}

export default Latest;
