import { Button } from '@/components/ui/Button';
import Image from 'next/image';
import Link from 'next/link';

const AboutSection = () => {
    return (
        <section className="w-full py-16 overflow-hidden"
            style={{
                backgroundImage: "url('/images/2.png')",
                backgroundSize: 'auto',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat'
            }}
        >
            <div className="container mx-auto px-4 md:px-6 lg:px-8 relative z-10"   >
                <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                    {/* Left Column - Text Content */}
                    <div className="md:w-1/2 space-y-6 text-center md:text-start">
                        <div className="space-y-2 pb-3 text-2xl sm:text-3xl md:text-4xl font-medium md:text-start text-gray-800">
                            <h2 >
                                Committed to
                                <span className="text-[#30527d]"> Quality</span>,
                            </h2>
                            <h2 >
                                Focused on
                                <span className="text-[#39A171]"> Growth</span>
                            </h2>
                        </div>

                        <div className="text-gray-700 space-y-4 pb-7">
                            <p>
                                Lorem ipsum dolor sit amet consectetur. Egestas eu suscipit turpis duis etiam est consectetur nullam enim. Sagittis sit vulputate.
                            </p>
                            <p>
                                Lorem ipsum dolor sit amet consectetur. Egestas eu suscipit turpis duis etiam est consectetur nullam enim. Sagittis sit vulputate.
                            </p>
                        </div>

                        <Link href="/about-us" className="inline-block">
                            <Button variant="primary" hasArrow={true} className='!w-56'>
                                See More About Us
                            </Button>
                        </Link>
                    </div>

                    {/* Right Column  */}
                    <div className="md:w-1/2 flex flex-col gap-4 items-center px-4 md:px-0">
                        {/* Main Image */}
                        <div className="md:w-[90%] overflow-hidden md:ms-[5%]">
                            <Image
                                src="/images/aboutImg1.png"
                                alt="Road construction with roller compactors"
                                width={560}
                                height={400}
                                className="md:w-full md:h-96 w-56 h-44 rounded-[10px] shadow-[0px_4px_21.200000762939453px_0px_rgba(0,0,0,0.15)] object-cover"
                            />
                        </div>
                        {/* Small Images */}
                        <div className="flex flex-row gap-10 md:gap-2 group mt-[-35%] w-[110%] md:w-[106%] md:ms-[-3%]">
                            <div className="w-[55%] rounded-lg overflow-hidden shadow-lg transition-transform duration-300 ease-in-out group-hover:translate-x-[-15px] group-hover:translate-y-10">
                                <Image
                                    src="/images/aboutimg2.png"
                                    alt="Construction surveyor"
                                    width={350}
                                    height={250}
                                    className="object-cover w-32 h-28 md:w-full md:h-64 rounded-[10px] shadow-[0px_4px_11.600000381469727px_0px_rgba(0,0,0,0.25)]"
                                />
                            </div>

                            <div className="w-[55%] rounded-lg overflow-hidden shadow-lg transition-transform duration-300 ease-in-out group-hover:translate-x-[15px] group-hover:translate-y-10">
                                <Image
                                    src="/images/aboutimg3.png"
                                    alt="Engineer working on plans"
                                    width={350}
                                    height={250}
                                    className="object-cover w-32 h-28 md:w-full md:h-64 rounded-[10px] shadow-[0px_4px_11.600000381469727px_0px_rgba(0,0,0,0.25)]"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default AboutSection;