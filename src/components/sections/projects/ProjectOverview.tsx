import InfoCard from "@/components/ui/InfoCard";
import { Project } from "@/types";


const ProjectOverview = ({
    name,
    location,
    duration
}: Project) => {
    return (
        <div className="relative w-full max-w-6xl mx-auto px-4 py-16">
            {/* Title */}
            <h2 className="text-center mb-16 text-neutral-700 text-3xl font-semibold">
                Project Overview
            </h2>

            {/* Main Content Container */}
            <div className="relative">
                {/* Info Cards Grid */}
                <div className="flex flex-col lg:flex-row gap-10 justify-around items-center">
                    {/* Client Card */}
                    <InfoCard
                        icon={"/images/clientIcon.svg"}
                        hoverIcon={"/images/clientHoverIcon.svg"}
                        title="Client"
                        primaryText={name}
                    />

                    {/* Duration Card */}
                    <InfoCard
                        icon={"/images/durationIcon.svg"}
                        hoverIcon={"/images/durationHoverIcon.svg"}
                        title="Duration"
                        primaryText={duration}
                    />

                    {/* Location Card */}
                    <InfoCard
                        icon={"/images/locationIcon.svg"}
                        hoverIcon={"/images/locationHoverIcon.svg"}
                        title="Location"
                        primaryText={location}
                    />
                </div>
            </div>
        </div>
    );
}

export default ProjectOverview