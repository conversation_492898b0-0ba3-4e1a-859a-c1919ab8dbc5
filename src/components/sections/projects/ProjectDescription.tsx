"use client"

import { Project } from '@/types';
import Image from 'next/image';
import { useState, useEffect, useCallback } from 'react';


const ProjectDescription = ({
    description,
    image = ["/images/projectGalleryImg.png"]
}: Project) => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [isAutoPlaying, setIsAutoPlaying] = useState(true);
    const images = Array.isArray(image) ? image : [image];

    const nextImage = useCallback(() => {
        setCurrentImageIndex((prev) => (prev + 1) % images.length);
    }, [images.length]);

    const goToImage = (index: number) => {
        setCurrentImageIndex(index);
        setIsAutoPlaying(false);
    };

    useEffect(() => {
        let interval: NodeJS.Timeout;

        if (isAutoPlaying && images.length > 1) {
            interval = setInterval(nextImage, 3000);
        }

        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [isAutoPlaying, nextImage, images.length]);

    return (
        <section className='py-8 sm:py-12 px-4 md:px-6 lg:px-8'>
            <div className="w-full container max-w-6xl mx-auto px-4 py-16">
                {/* Main Content Container */}
                <div className="flex flex-col lg:flex-row gap-10 items-start">
                    {/* Left Side - Description */}
                    <div className="flex-[45%] text-center lg:text-start mb-8 lg:mb-0">
                        <h3 className="text-2xl font-semibold text-black mb-6 ">
                            Project Description
                        </h3>
                        <p className="leading-relaxed text-black text-base font-normal">
                            {description}
                        </p>
                    </div>

                    {/* Right Side - Image Slider */}
                    <div className="flex-[55%] w-full">
                        <div
                            className="relative w-full h-[300px] sm:h-[400px] md:h-[460px]"
                            onMouseEnter={() => setIsAutoPlaying(false)}
                            onMouseLeave={() => setIsAutoPlaying(true)}
                        >
                            {/* Top-right bracket */}
                            <div className="absolute -top-10 -right-10 z-10">
                                <Image
                                    src={"/images/ImageCornerRight.svg"}
                                    alt="ImageCornerRight icon"
                                    width={32}
                                    height={32}
                                    className="w-20 h-20 sm:w-28 sm:h-28"
                                />
                            </div>

                            {/* Image Container */}
                            <div className="relative w-full h-full overflow-hidden rounded-lg">
                                <div
                                    className="flex transition-transform duration-300 ease-in-out h-full w-full"
                                    style={{ transform: `translateX(-${currentImageIndex * 100}%)` }}
                                >
                                    {images.map((img: string, index: number) => (
                                        <div key={index} className="w-full h-full flex-shrink-0 relative">
                                            <Image
                                                src={img}
                                                alt={`Project image ${index + 1}`}
                                                fill
                                                className="object-cover rounded-lg"
                                                sizes="(max-width: 640px) 100vw, (max-width: 768px) 80vw, 60vw"
                                            />
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Bottom-left bracket */}
                            <div className="absolute -bottom-10 -left-10 z-10">
                                <Image
                                    src={"/images/ImgCornerLeft.svg"}
                                    alt="ImgCornerLeft icon"
                                    width={32}
                                    height={32}
                                    className="w-20 h-20 sm:w-28 sm:h-28"
                                />
                            </div>

                            {/* Dot Indicators - Only show if more than 1 image */}
                            {images.length > 1 && (
                                <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                                    {images.map((_, index: number) => (
                                        <button
                                            key={index}
                                            onClick={() => goToImage(index)}
                                            className={`w-3 h-3 rounded-full transition-all duration-200 ${index === currentImageIndex
                                                ? 'bg-[#365D92] shadow-lg'
                                                : 'bg-[#CFCFCF] hover:bg-[#365D92] hover:cursor-pointer'
                                                }`}
                                            aria-label={`Go to image ${index + 1}`}
                                        />
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default ProjectDescription