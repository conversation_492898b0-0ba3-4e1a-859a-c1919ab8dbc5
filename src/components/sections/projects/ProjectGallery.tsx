"use client";

import React, { useState } from "react";
import { Search } from "lucide-react";
import { filterOptions, projects } from "@/messages/projects";
import ProjectCard from "@/components/ui/ProjectCard";

const ProjectGallery = () => {
  const [activeFilter, setActiveFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");

  const filteredProjects = projects.filter((project) => {
    const matchesFilter =
      activeFilter === "all" ||
      project.sector.toLowerCase().replace(" ", "") === activeFilter;
    const matchesSearch =
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.location.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  return (
    <section className=" py-8 sm:py-12 md:py-16 px-4 md:px-6 lg:px-8 md:mt-0">
      <div className="container mx-auto">
        {/* Header */}
        <h1 className="text-3xl font-medium text-center text-black mb-12">
          All Projects
        </h1>

        {/* Filter and Search Section */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-6">
          {/* Filter Options */}
          <div className="flex flex-wrap items-center justify-between gap-2">
            <span className="text-black text-xl font-medium me-4">
              Filter by
            </span>
            {filterOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setActiveFilter(option.value)}
                className={`px-4 rounded-lg font-medium ${activeFilter === option.value
                    ? "text-[#365D92]"
                    : "text-black hover:text-[#365D92] cursor-pointer"
                  }`}
              >
                {option.label}
                <span className="ms-2 text-xs opacity-75 absolute">
                  {option.count}
                </span>
              </button>
            ))}
          </div>

          {/* Search Bar */}
          <div className="relative w-80">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#ABABAB]  w-5 h-5" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full ps-10 pe-4 py-3 border border-[#ABABAB] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-1 outline-offset-[-1px] outline-[#B8B8B8] rounded-lg "
            />
          </div>
        </div>

        {/* Project Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-15">
          {filteredProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>

        {/* No Results */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-16">
            <p className="text-gray-500 text-lg">
              No projects found matching your criteria.
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProjectGallery;
