import { NextProjectSectionProps } from '@/types';
import Image from 'next/image';
import Link from 'next/link';


const NextProject = ({
    title,
    service,
    subService,
    imageUrl,
    projectUrl
}: NextProjectSectionProps) => {
    return (
        <section className="py-8 sm:py-12 px-4 md:px-6 lg:px-8">
            <div className="w-full container max-w-6xl mx-auto px-4">
                {/* Next Project Header */}
                <div className="flex items-center gap-3 mb-8 justify-center lg:justify-start ">
                    <h2 className="text-3xl font-semibold text-[#2A5790]">
                        Next Project
                    </h2>
                    {/* rightArrow.svg */}
                    <Image
                        src='/images/rightArrow.svg'
                        alt={'right Arrow Icon '}
                        className="w-10 h-10 md:w-14 md:h-20 object-cover"
                        width={10}
                        height={10}
                    />
                </div>

                {/* Project Preview Card */}
                <Link href={projectUrl} className="block group">
                    <div className="flex flex-col lg:flex-row gap-8 hover:transform hover:scale-[1.02] transition-all duration-700">
                        {/* Project Image */}
                        <div className="flex-shrink-0 w-full lg:w-[435px]">
                            <div className="relative w-full h-[280px] overflow-hidden rounded-lg">
                                <Image
                                    src={imageUrl}
                                    alt={title}
                                    fill
                                    className="object-cover group-hover:scale-105 transition-transform duration-700"
                                />
                            </div>
                        </div>

                        {/* Project Details */}
                        <div className="flex-1 pt-4">
                            <h3 className="text-2xl font-medium text-black mb-6 leading-relaxed group-hover:text-[#2A5790] group-hover:underline transition-colors duration-700">
                                {title}
                            </h3>

                            <div className="text-[#365D92] text-2xl font-light group-hover:font-medium transition-all duration-700">
                                {service}
                                {subService && (
                                    <span>. {subService}</span>
                                )}
                            </div>
                        </div>
                    </div>
                </Link>
            </div>
        </section>
    );
};

export default NextProject;