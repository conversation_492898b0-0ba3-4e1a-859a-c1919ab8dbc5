import { Button } from '@/components/ui/Button';
import { projectFeatures } from '@/messages/projects';
import Image from 'next/image';
import Link from 'next/link';

const ProjectInformation = () => {
    return (
        <section className="py-8 sm:py-12 md:py-16 px-4 md:px-6 lg:px-8 mt-[15%] md:mt-0">
            <div className="container mx-auto">
                <div className="flex flex-col items-start gap-5 [@media(min-width:820px)]:flex-row justify-between">
                    {/* Left Column - Images */}
                    <div className="w-full [@media(min-width:820px)]:w-[60%] [@media(min-width:820px)]:flex-shrink-0 relative mb-12 [@media(min-width:820px)]:mb-0">
                        {/* Container for proper spacing on mobile */}
                        <div className="relative w-full h-[455px]">
                            <Image
                                src="/images/projectInfoImg3.svg"
                                alt="project Info Img"
                                fill
                                className="object-cover rounded-3xl z-10"
                            />
                        </div>
                        {/* Message Box */}
                        <div className="absolute top-80 -left-20 w-32 h-20 [@media(min-width:820px)]:w-40 [@media(min-width:820px)]:h-24 bg-[#39A171] rounded-xl z-20 p-2 [@media(min-width:820px)]:p-4 shadow-lg flex flex-row items-center">
                            <Image
                                src="/images/projectIcon.svg"
                                alt="project Img"
                                className="w-8 h-8 [@media(min-width:820px)]:w-10 [@media(min-width:820px)]:h-10 object-cover"
                                width={20}
                                height={20}
                            />
                            <div className='flex flex-col items-center'>
                                {/* Number indicator */}
                                <span className="text-white font-bold text-lg [@media(min-width:820px)]:text-xl">+0</span>
                                {/* Message text */}
                                <p className="text-white text-[7px] [@media(min-width:820px)]:text-[8px] font-normal">
                                    Lorem ipsum dolor sit amet consectetur
                                </p>
                            </div>
                            {/* Pointer triangle */}
                            <div className="absolute top-1/2 -right-2 w-4 h-4 bg-[#39A171] transform translate-y-4/5 rotate-45 z-10"></div>
                        </div>
                    </div>

                    {/* Right Column - Text Content */}
                    <div className="w-full [@media(min-width:820px)]:w-[40%] text-center [@media(min-width:820px)]:text-start">
                        <div className="flex flex-col items-center [@media(min-width:820px)]:items-start gap-4 mb-12">
                            <p className='self-start text-[#365D92] text-base font-semibold'>project sector</p>
                            <h2 className="text-md md:text-2xl font-semibold">Lorem ipsum dolor sit amet consectetur. Tincidunt sed posuere donec</h2>
                            <p className="text-sm md:text-base font-normal text-[#666666] mb-4">
                                Lorem ipsum dolor sit amet consectetur. Lobortis vulputate quis eu lacus in et eget ridiculus ultricies. Id morbi quam arcu Lorem ipsum dolor sit amet consectetur. Lobortis vulputate quis eu lacus.
                            </p>
                            <div className='flex flex-col gap-3 w-full'>
                                {projectFeatures.map((feature, index) => (
                                    <div key={index} className="flex items-center gap-3">
                                        <div className="w-3.5 h-3.5 relative bg-gradient-to-b from-[#54AD84] to-[#6798D4] rounded-full me-2"></div>
                                        <p className="text-xs md:text-sm text-[#666666] font-semibold">{feature}</p>
                                    </div>
                                ))}
                                <Link href="/contact" className='mt-9 self-center [@media(min-width:820px)]:self-start'>
                                    <Button variant="outline">See More</Button>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default ProjectInformation