import React from 'react';
import { MapPin, Mail, Phone } from 'lucide-react';
import { BranchLocationCardProps } from '@/types';

const BranchLocationCard = ({
    name,
    country,
    address,
    email,
    phone
}: BranchLocationCardProps) => {
    return (
        <div className="bg-white rounded-lg p-4 sm:p-6 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.10)] border border-gray-200">
            <div className="flex flex-col sm:flex-row items-start sm:space-x-4 space-y-4 sm:space-y-0">
                <div className="flex-shrink-0 self-center sm:self-start">
                    <div className="w-10 h-10 bg-[#C3DEFF] rounded-full flex items-center justify-center">
                        <MapPin className="w-5 h-5 text-[#365D92]" />
                    </div>
                </div>
                <div className="flex-1 min-w-0 w-full">
                    <h3 className="text-lg sm:text-xl font-medium text-black mb-2 break-words text-center sm:text-left">
                        {name}, {country}
                    </h3>
                    <p className="text-black mb-4 text-sm font-normal font-['Roboto'] capitalize leading-tight break-words text-center sm:text-left">
                        {address}
                    </p>
                    <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-6">
                        <div className="flex items-center gap-2 min-w-0 w-full sm:w-auto justify-center sm:justify-start">
                            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-[#C3DEFF] rounded-full flex items-center justify-center flex-shrink-0">
                                <Mail className="w-3 h-3 sm:w-4 sm:h-4 text-[#365D92]" />
                            </div>
                            <span className="text-black text-xs sm:text-sm font-normal font-['Roboto'] lowercase leading-relaxed break-all">
                                {email}
                            </span>
                        </div>
                        <div className="flex items-center gap-2 min-w-0 w-full sm:w-auto justify-center sm:justify-start">
                            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-[#C3DEFF] rounded-full flex items-center justify-center flex-shrink-0">
                                <Phone className="w-3 h-3 sm:w-4 sm:h-4 text-[#365D92]" />
                            </div>
                            <span className="text-black text-xs sm:text-sm font-normal font-['Roboto'] lowercase leading-relaxed break-all">
                                {phone}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default BranchLocationCard;