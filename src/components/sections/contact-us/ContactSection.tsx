"use client";

import React, { useState } from "react";
import Image from "next/image";
import { FormData } from "@/types";
import { branches } from "@/data/contact";
import BranchLocationCard from "./BranchLocationCard";
import { Button } from "@/components/ui/Button";

const ContactSection = () => {
  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    company: "",
    role: "",
    message: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateEmail = (email: string): string => {
    if (!email.trim()) return "Email is required";
    if (!/\S+@\S+\.\S+/.test(email)) return "Email is invalid";
    return "";
  };

  const validatePhone = (phone: string): string => {
    if (!phone.trim()) return "Phone number is required";
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, "");
    if (!cleanPhone.startsWith("+"))
      return "Phone number must start with country code (e.g., +20)";
    if (!/^\+\d+$/.test(cleanPhone))
      return "Phone number must contain only numbers after country code";
    if (cleanPhone.length < 13)
      return "Phone number must be at least 13 digits including country code";
    return "";
  };

  // Validate individual field
  const validateField = (name: string, value: string): string => {
    switch (name) {
      case "firstName":
        return !value.trim() ? "First name is required" : "";
      case "lastName":
        return !value.trim() ? "Last name is required" : "";
      case "email":
        return validateEmail(value);
      case "phone":
        return validatePhone(value);
      case "company":
        return !value.trim() ? "Company name is required" : "";
      case "message":
        return !value.trim() ? "Message is required" : "";
      default:
        return "";
    }
  };

  // handle Input Change
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Validate the field that changed
    const error = validateField(name, value);
    setErrors((prev) => ({
      ...prev,
      [name]: error,
    }));
  };

  // form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate all fields
    Object.keys(formData).forEach((key) => {
      const error = validateField(key, formData[key as keyof FormData]);
      if (error) {
        newErrors[key] = error;
      }
    });

    return newErrors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      // Format the email body with form data
      const emailBody = `
            Name: ${formData.firstName} ${formData.lastName}\n Email: ${formData.email}\n Phone: ${formData.phone}\n Company: ${formData.company}\n Role: ${formData.role}\n Message:${formData.message}`.trim();

      // Encode the email body for URL
      const encodedBody = encodeURIComponent(emailBody);
      const encodedSubject = encodeURIComponent("Contact Form Submission");

      // Redirect to email client
      window.location.href = `mailto:<EMAIL>?subject=${encodedSubject}&body=${encodedBody}`;

      // Reset form on success
      setFormData({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        company: "",
        role: "",
        message: "",
      });
      setErrors({});
    } catch (error) {
      console.error("Submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const inputBaseStyle =
    "w-full px-4 py-3 border rounded-lg focus:ring-1 focus:ring-[#365D92] focus:border-transparent transition-colors";
  const getInputStyle = (fieldName: string) =>
    `${inputBaseStyle} ${
      errors[fieldName] ? "border-red-500" : "border-gray-300"
    }`;

  return (
    <section className="py-8 sm:py-12 px-4 md:px-6 lg:px-8">
      <div className="container max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 mt-20 md:mt-0">
          {/* Left Column - Get In Touch */}
          <div className="lg:col-span-6 w-full lg:w-[90%]">
            <h2 className="text-2xl font-semibold text-black mb-4">
              Get In touch
            </h2>
            <p className="text-[#666666] text-base font-normal mb-8">
              Lorem ipsum dolor sit amet consectetur. Fermentum lacus
            </p>

            {/* Branch Locations */}
            <div className="space-y-6">
              {branches.map((branch) => (
                <BranchLocationCard
                  key={branch.id}
                  name={branch.name}
                  country={branch.country}
                  address={branch.address}
                  email={branch.email}
                  phone={branch.phone}
                />
              ))}
            </div>
          </div>

          {/* Right Column - Contact Form */}
          <div className="lg:col-span-6">
            <h2 className="text-2xl font-semibold text-black mb-4">
              Have a Question? Send Us a Message
            </h2>
            <p className="text-[#666666] text-base font-normal mb-8">
              Lorem ipsum dolor sit amet consectetur. Fermentum lacus quam
              integer aenean.Lorem ipsum
            </p>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name Fields */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="firstName"
                    className="block text-sm font-medium text-[#666666] mb-2"
                  >
                    First Name *
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={getInputStyle("firstName")}
                    autoComplete="given-name"
                  />
                  {errors.firstName && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.firstName}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="lastName"
                    className="block text-sm font-medium text-[#666666] mb-2"
                  >
                    Last Name *
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={getInputStyle("lastName")}
                    autoComplete="family-name"
                  />
                  {errors.lastName && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.lastName}
                    </p>
                  )}
                </div>
              </div>

              {/* Email */}
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-[#666666] mb-2"
                >
                  Email *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={getInputStyle("email")}
                  autoComplete="email"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              {/* Phone */}
              <div>
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-[#666666] mb-2"
                >
                  Phone NO. *
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className={getInputStyle("phone")}
                  autoComplete="tel"
                />
                {errors.phone && (
                  <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                )}
              </div>

              {/* Company Name */}
              <div>
                <label
                  htmlFor="company"
                  className="block text-sm font-medium text-[#666666] mb-2"
                >
                  Company Name *
                </label>
                <input
                  type="text"
                  id="company"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  className={getInputStyle("company")}
                  autoComplete="organization"
                />
                {errors.company && (
                  <p className="mt-1 text-sm text-red-600">{errors.company}</p>
                )}
              </div>

              {/* Role */}
              <div>
                <label
                  htmlFor="role"
                  className="block text-sm font-medium text-[#666666] mb-2"
                >
                  Role
                </label>
                <input
                  type="text"
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-1 focus:ring-[#365D92] focus:border-transparent transition-colors"
                  autoComplete="organization-title"
                />
              </div>

              {/* Message */}
              <div>
                <label
                  htmlFor="message"
                  className="block text-sm font-medium text-[#666666] mb-2"
                >
                  Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={6}
                  value={formData.message}
                  onChange={handleInputChange}
                  className={`${getInputStyle("message")} resize-none`}
                />
                {errors.message && (
                  <p className="mt-1 text-sm text-red-600">{errors.message}</p>
                )}
              </div>

              <Button
                type="submit"
                variant="primary"
                hasArrow
                disabled={isSubmitting}
              >
                {isSubmitting ? "Sending..." : "Send a Message"}
              </Button>
            </form>
          </div>
        </div>

        {/* Map Section */}
        <div className="mt-16">
          <div className="bg-gray-200 rounded-lg h-96 relative overflow-hidden">
            <Image
              src="/images/mapImg.png"
              alt="Office locations map"
              fill
              className="object-cover"
              loading="lazy"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
