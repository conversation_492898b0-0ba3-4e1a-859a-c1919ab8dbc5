import { FilterOption, News } from "@/types";

export const news: News[] = [
    { 
        id: 1, 
        title: 'Highway Bridge Project', 
        date: new Date('2025-01-01'), 
        type:"News",
        image: [
            '/images/news1.jpg',
            '/images/news2.png',
            '/images/news3.png',
            '/images/news4.png',
            '/images/news4.png',
        ], 
        description: `quisque augue elementum tempus fames ipsum tincidunt at. Cursus pulvinar senectus adipiscing hac enim in cras facilisi. Ut a tortor enim pellentesque donec feugiat eros faucibus viverra. Facilisis suscipit quis egestas diam semper. Lorem scelerisque viverra vestibulum justo in amet ultrices ipsum. Orci egestas luctus sed eget scelerisque sit interdum. Consectetur felis quis tempor id .
        

        bibendum viverra ultrices. Fermentum duis fermentum morbi diam eget nam velit tincidunt auctor. Nibh cursus malesuada quisque volutpat cursus. Diam consequat enim sed vestibulum tincidunt cursus gravida fermentum ut. Netus arcu pharetra gravida mi ornare. Cursus fringilla condimentum tellus sit. Vel tortor nisi velit in felis. Vitae arcu lorem donec faucibus laoreet sed et praesent. Eget diam suspendisse faucibus eu lacus tristique ornare..


        Ac erat mauris vitae ac condimentum nulla. Quisque est fermentum commodo nulla. Sit ultrices velit aliquam lectus convallis ipsum duis. Aliquam urna sit enim rhoncus. Auctor dui aenean est malesuada. Vitae consectetur maecenas pellentesque tellus mauris metus facilisis. Elementum dolor sollicitudin arcu aliquam nec id. Elementum sed libero eget ut diam aliquam.
        `,
        subDescription: 'Located in the heart of Downtown. A transformative project enhancing urban connectivity.',
    },
    { 
        id: 2, 
        title: 'Urban Infrastructure', 
        date: new Date('2024-12-01'),
        type:"News",
        image: [
            '/images/sector1.svg',
            '/images/sector1.svg',
            '/images/sector1.svg'
        ], 
        description: `quisque augue elementum tempus fames ipsum tincidunt at. Cursus pulvinar senectus adipiscing hac enim in cras facilisi. Ut a tortor enim pellentesque donec feugiat eros faucibus viverra. Facilisis suscipit quis egestas diam semper. Lorem scelerisque viverra vestibulum justo in amet ultrices ipsum. Orci egestas luctus sed eget scelerisque sit interdum. Consectetur felis quis tempor id.
        

        bibendum viverra ultrices. Fermentum duis fermentum morbi diam eget nam velit tincidunt auctor. Nibh cursus malesuada quisque volutpat cursus. Diam consequat enim sed vestibulum tincidunt cursus gravida fermentum ut. Netus arcu pharetra gravida mi ornare. Cursus fringilla condimentum tellus sit. Vel tortor nisi velit in felis. Vitae arcu lorem donec faucibus laoreet sed et praesent. Eget diam suspendisse faucibus eu lacus tristique ornare.


        Ac erat mauris vitae ac condimentum nulla. Quisque est fermentum commodo nulla. Sit ultrices velit aliquam lectus convallis ipsum duis. Aliquam urna sit enim rhoncus. Auctor dui aenean est malesuada. Vitae consectetur maecenas pellentesque tellus mauris metus facilisis. Elementum dolor sollicitudin arcu aliquam nec id. Elementum sed libero eget ut diam aliquam.
        `,
        subDescription: 'Centered in the City Center district. Modernizing essential urban services and infrastructure.'
    },
    { 
        id: 3, 
        title: 'Transportation Hub', 
        date: new Date('2024-11-15'),
        type:"Events",
        image: [
            '/images/sector1.svg',
            '/images/sector1.svg',
            '/images/sector1.svg'
        ], 
        description: `quisque augue elementum tempus fames ipsum tincidunt at. Cursus pulvinar senectus adipiscing hac enim in cras facilisi. Ut a tortor enim pellentesque donec feugiat eros faucibus viverra. Facilisis suscipit quis egestas diam semper. Lorem scelerisque viverra vestibulum justo in amet ultrices ipsum. Orci egestas luctus sed eget scelerisque sit interdum. Consectetur felis quis tempor id.
        

        bibendum viverra ultrices. Fermentum duis fermentum morbi diam eget nam velit tincidunt auctor. Nibh cursus malesuada quisque volutpat cursus. Diam consequat enim sed vestibulum tincidunt cursus gravida fermentum ut. Netus arcu pharetra gravida mi ornare. Cursus fringilla condimentum tellus sit. Vel tortor nisi velit in felis. Vitae arcu lorem donec faucibus laoreet sed et praesent. Eget diam suspendisse faucibus eu lacus tristique ornare.


        Ac erat mauris vitae ac condimentum nulla. Quisque est fermentum commodo nulla. Sit ultrices velit aliquam lectus convallis ipsum duis. Aliquam urna sit enim rhoncus. Auctor dui aenean est malesuada. Vitae consectetur maecenas pellentesque tellus mauris metus facilisis. Elementum dolor sollicitudin arcu aliquam nec id. Elementum sed libero eget ut diam aliquam.
        `,
        subDescription: 'Situated in North District. A modern hub connecting multiple transportation networks.'
    },
    { 
        id: 4, 
        title: 'Road Development', 
        date: new Date('2024-10-01'),
        type:"Events",
        image: [
            '/images/sector1.svg',
            '/images/sector1.svg',
            '/images/sector1.svg'
        ], 
        description: `quisque augue elementum tempus fames ipsum tincidunt at. Cursus pulvinar senectus adipiscing hac enim in cras facilisi. Ut a tortor enim pellentesque donec feugiat eros faucibus viverra. Facilisis suscipit quis egestas diam semper. Lorem scelerisque viverra vestibulum justo in amet ultrices ipsum. Orci egestas luctus sed eget scelerisque sit interdum. Consectetur felis quis tempor id .
        

        bibendum viverra ultrices. Fermentum duis fermentum morbi diam eget nam velit tincidunt auctor. Nibh cursus malesuada quisque volutpat cursus. Diam consequat enim sed vestibulum tincidunt cursus gravida fermentum ut. Netus arcu pharetra gravida mi ornare. Cursus fringilla condimentum tellus sit. Vel tortor nisi velit in felis. Vitae arcu lorem donec faucibus laoreet sed et praesent. Eget diam suspendisse faucibus eu lacus tristique ornare..


        Ac erat mauris vitae ac condimentum nulla. Quisque est fermentum commodo nulla. Sit ultrices velit aliquam lectus convallis ipsum duis. Aliquam urna sit enim rhoncus. Auctor dui aenean est malesuada. Vitae consectetur maecenas pellentesque tellus mauris metus facilisis. Elementum dolor sollicitudin arcu aliquam nec id. Elementum sed libero eget ut diam aliquam.
        `,
        subDescription: 'Located in East Side. Enhancing road safety and traffic flow efficiency.'
    },
    { 
        id: 5, 
        title: 'Bridge Construction', 
        date: new Date('2024-09-15'),
        type:"Events",
        image: [
            '/images/sector1.svg',
            '/images/sector1.svg',
            '/images/sector1.svg'
        ], 
        description: `quisque augue elementum tempus fames ipsum tincidunt at. Cursus pulvinar senectus adipiscing hac enim in cras facilisi. Ut a tortor enim pellentesque donec feugiat eros faucibus viverra. Facilisis suscipit quis egestas diam semper. Lorem scelerisque viverra vestibulum justo in amet ultrices ipsum. Orci egestas luctus sed eget scelerisque sit interdum. Consectetur felis quis tempor id .
        

        bibendum viverra ultrices. Fermentum duis fermentum morbi diam eget nam velit tincidunt auctor. Nibh cursus malesuada quisque volutpat cursus. Diam consequat enim sed vestibulum tincidunt cursus gravida fermentum ut. Netus arcu pharetra gravida mi ornare. Cursus fringilla condimentum tellus sit. Vel tortor nisi velit in felis. Vitae arcu lorem donec faucibus laoreet sed et praesent. Eget diam suspendisse faucibus eu lacus tristique ornare..


        Ac erat mauris vitae ac condimentum nulla. Quisque est fermentum commodo nulla. Sit ultrices velit aliquam lectus convallis ipsum duis. Aliquam urna sit enim rhoncus. Auctor dui aenean est malesuada. Vitae consectetur maecenas pellentesque tellus mauris metus facilisis. Elementum dolor sollicitudin arcu aliquam nec id. Elementum sed libero eget ut diam aliquam.
        `,
        subDescription: 'Spanning across West End. A landmark bridge combining aesthetics with engineering excellence.'
    },
    { 
        id: 6, 
        title: 'Highway Extension', 
        date: new Date('2024-08-01'),
        type:"Events",
        image: [
            '/images/sector1.svg',
            '/images/sector1.svg',
            '/images/sector1.svg'
        ], 
        description: `quisque augue elementum tempus fames ipsum tincidunt at. Cursus pulvinar senectus adipiscing hac enim in cras facilisi. Ut a tortor enim pellentesque donec feugiat eros faucibus viverra. Facilisis suscipit quis egestas diam semper. Lorem scelerisque viverra vestibulum justo in amet ultrices ipsum. Orci egestas luctus sed eget scelerisque sit interdum. Consectetur felis quis tempor id .
        

        bibendum viverra ultrices. Fermentum duis fermentum morbi diam eget nam velit tincidunt auctor. Nibh cursus malesuada quisque volutpat cursus. Diam consequat enim sed vestibulum tincidunt cursus gravida fermentum ut. Netus arcu pharetra gravida mi ornare. Cursus fringilla condimentum tellus sit. Vel tortor nisi velit in felis. Vitae arcu lorem donec faucibus laoreet sed et praesent. Eget diam suspendisse faucibus eu lacus tristique ornare..


        Ac erat mauris vitae ac condimentum nulla. Quisque est fermentum commodo nulla. Sit ultrices velit aliquam lectus convallis ipsum duis. Aliquam urna sit enim rhoncus. Auctor dui aenean est malesuada. Vitae consectetur maecenas pellentesque tellus mauris metus facilisis. Elementum dolor sollicitudin arcu aliquam nec id. Elementum sed libero eget ut diam aliquam.
        `,
        subDescription: 'Extending through South Zone. Creating vital connections for regional development.'
    },
];

export const filterOptions: FilterOption[] = [
    { label: 'All', value: 'all', count: 6 },
    { label: 'News', value: 'news', count: 2 },
    { label: 'Events', value: 'events', count: 4 }
];

export const AutoSliderImages= [
    '/images/news1.jpg',
    '/images/news2.png',
    '/images/news3.png',
    '/images/news4.png',
    '/images/news4.png',
];

export const socialIcons = [
    { src: '/images/linkedInIcon1.svg', label: 'LinkedIn' },
    { src: '/images/facebookIcon.svg', label: 'Facebook' },
    { src: '/images/twitterIcon1.svg', label: 'X (Twitter)' },
    { src: '/images/instaIcon1.svg', label: 'Instagram' }
];