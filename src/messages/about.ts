import { GovernanceItem } from "@/types";

export const memberData = [
    {
        id: 1,
        imageUrl: "/images/member1.jpg", 
        name: "<PERSON>",
        title: "Board Chairman",
        about: "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu.Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit ",
        achievements: "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu.Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit ",
        links: {
            youtube: "https://youtube.com/user/johnsmith",
            linkedin: "https://linkedin.com/in/johnsmith",
            bio: "/bios/john-smith"
        }
    },
    {
        id: 2,
        imageUrl: "/images/member2.png",
        name: "Sarah Johnson",
        title: "Vice Chairperson",
        about: "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu.Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit ",
        achievements: "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu.Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit ",
        links: {
            linkedin: "https://linkedin.com/in/sarahjohnson",
            bio: "/bios/sarah-johnson"
        }
    },
    {
        id: 3,
        imageUrl: "/images/member3.png",
        name: "Michael Chen",
        title: "Financial Director",
        about: "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu.Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit ",
        achievements: "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu.Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit ",
        links: {
            youtube: "https://youtube.com/user/michaelchen",
            linkedin: "https://linkedin.com/in/michaelchen"
        }
    },
    {
        id: 4,
        imageUrl: "/images/member4.png",
        name: "Emily Rodriguez",
        title: "Operations Director",
        about: "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu.Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit ",
        achievements: "Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit eu eu ultrices quam sed lacus vestibulum sit quam. Feugiat nibh diam adipiscing tellus proin praesent ornare placerat lacus. Purus placerat eros viverra tellus duis arcu.Lorem ipsum dolor sit amet consectetur. Iaculis platea eget nisl tortor consequat nibh vel adipiscing. Blandit ",
        links: {
            linkedin: "https://linkedin.com/in/emilyrodriguez",
            bio: "/bios/emily-rodriguez"
        }
    },
    
];

export const governanceItems: GovernanceItem[] = [
    {
        title: "The company's Classification certificate",
        content: "test"
    },
    {
        title: "The company's Classification certificate",
        content: "test"
    },
    {
        title: "The company's Classification certificate",
        content: "test"
    },
    {
        title: "The company's Classification certificate",
        content: "test"
    }
];

export const certifications = [
    { isoNumber: "ISO 9001", date: "12/12/2024" },
    { isoNumber: "ISO 14001", date: "12/12/2024" },
    { isoNumber: "ISO 45001", date: "12/12/2024" }
];