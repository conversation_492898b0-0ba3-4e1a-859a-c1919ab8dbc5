import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';
import { NextRequest } from 'next/server';

export default function middleware(request: NextRequest) {
  console.log('Middleware running for:', request.nextUrl.pathname);
  console.log('Request URL:', request.url);
  console.log('Routing config:', routing);

  const handleI18nRouting = createMiddleware(routing);
  const response = handleI18nRouting(request);

  console.log('Response status:', response.status);
  console.log('Response headers:', Object.fromEntries(response.headers.entries()));

  return response;
}

export const config = {
  // Match only internationalized pathnames
  matcher: '/((?!api|_next/static|_next/image|favicon.ico).*)'
};