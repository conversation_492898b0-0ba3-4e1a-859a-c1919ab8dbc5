import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';
import { NextRequest } from 'next/server';

const handleI18nRouting = createMiddleware(routing);

export default function middleware(request: NextRequest) {
  // Skip middleware for static assets
  const pathname = request.nextUrl.pathname;

  if (
    pathname.startsWith('/images/') ||
    pathname.startsWith('/fonts/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.match(/\.(png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i)
  ) {
    return;
  }

  // Apply i18n middleware for all other requests
  return handleI18nRouting(request);
}

export const config = {
  // Match all paths except Next.js internals
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)'
  ]
};