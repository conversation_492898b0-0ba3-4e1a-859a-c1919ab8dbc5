import { defineRouting } from 'next-intl/routing';
import { createNavigation } from 'next-intl/navigation';

export const routing = defineRouting({
    // A list of all locales that are supported
    locales: ['en', 'ar'],

    // Used when no locale matches
    defaultLocale: 'en',

    // The `pathnames` object holds pairs of internal and
    // external paths. Based on the locale, the external
    // paths are rewritten to the shared, internal ones.
    pathnames: {
        // '/': {
        //     en: '/Home',
        //     ar: '/الرئيسية'
        // }
        '/': '/'
        ,
        '/about-us': {
            en: '/about-us',
            ar: '/من-نحن'
        },
        '/services': {
            en: '/services',
            ar: '/الخدمات'
        },
        '/equipment': {
            en: '/equipment',
            ar: '/المعدات'
        },
        '/projects': {
            en: '/projects',
            ar: '/المشاريع'
        },
        '/news': {
            en: '/news',
            ar: '/الأخبار'
        },
        '/careers': {
            en: '/careers',
            ar: '/الوظائف'
        },
        '/contact-us': {
            en: '/contact-us',
            ar: '/اتصل-بنا'
        },
    }
});

// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const { Link, redirect, usePathname, useRouter } =
createNavigation(routing);

