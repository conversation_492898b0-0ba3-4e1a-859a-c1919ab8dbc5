import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

export default getRequestConfig(async ({ locale }) => {
    // If locale is undefined, use default locale
    let validLocale = locale;

    if (!validLocale) {
        validLocale = routing.defaultLocale;
    }

    // Validate that the incoming `locale` parameter is valid
    if (!routing.locales.includes(validLocale as any)) {
        validLocale = routing.defaultLocale;
    }

    return {
        locale: validLocale,
        messages: (await import(`../data/${validLocale}/index.ts`)).default
    };
});