import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

export default getRequestConfig(async ({ locale }) => {
    console.log('getRequestConfig called with locale:', locale);

    // If locale is undefined, try to get it from headers or use default
    let validLocale = locale;

    if (!validLocale) {
        console.log('Locale is undefined, using default locale');
        validLocale = routing.defaultLocale;
    }

    // Validate that the incoming `locale` parameter is valid
    if (!routing.locales.includes(validLocale as any)) {
        console.log(`Invalid locale: ${validLocale}, using default: ${routing.defaultLocale}`);
        validLocale = routing.defaultLocale;
    }

    console.log(`Final validLocale is ${validLocale}`);

    return {
        locale: validLocale,
        messages: (await import(`../data/${validLocale}/index.ts`)).default
    };
});