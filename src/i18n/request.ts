import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
    // Get locale from the request
    let locale = await requestLocale;

    // If locale is undefined, use default locale
    if (!locale) {
        locale = routing.defaultLocale;
    }

    // Validate that the incoming `locale` parameter is valid
    if (!routing.locales.includes(locale as any)) {
        locale = routing.defaultLocale;
    }

    console.log('getRequestConfig called with requestLocale:', await requestLocale);
    console.log('Final locale is', locale);

    const messages = (await import(`../messages/${locale}/index.ts`)).default;
    console.log('Loaded messages for', locale, ':', messages.Navigation);

    return {
        locale,
        messages
    };
});