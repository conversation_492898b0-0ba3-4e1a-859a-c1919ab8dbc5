import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

export default getRequestConfig(async ({ locale }) => {
    // Validate that the incoming `locale` parameter is valid
    if (!locale || !routing.locales.includes(locale as any)) {
        throw new Error(`Invalid locale: ${locale}`);
    }

    // Use the default locale if the provided one is invalid
    const validLocale = routing.locales.includes(locale as any) ? locale : routing.defaultLocale;

    console.log(`validLocale is ${validLocale}`)
    return {
        locale: validLocale,
        messages: (await import(`../data/${validLocale}/index.ts`)).default
    };
}); 