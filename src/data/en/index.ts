export default {
        "Navigation": {
            "home": "Home",
            "about": "About Us",
            "services": "Services",
            "projects": "Projects",
            "news": "News",
            "careers": "Careers",
            "contact": "Contact Us"
        },
        "Slider": [
            {
                "title": "Designing the Future,",
                "description": "Lorem ipsum dolor sit amet consectetur. Phasellus arcu neque porttitor varius.Lorem ipsum dolor sit amet consectetur.",
                "projectName": "Project name - city",
                "backgroundImage": "/images/hero-1.png"
            },
            {
                "title": "Innovative Architecture,",
                "description": "Creating sustainable spaces that inspire and transform communities while prioritizing environmental integrity.",
                "projectName": "Skyline Tower - Dubai",
                "backgroundImage": "/images/hero-2.png"
            },
            {
                "title": "Engineering Marvels,",
                "description": "Pushing the boundaries of what's possible through cutting-edge engineering and visionary design thinking.",
                "projectName": "Riverside Development - London",
                "backgroundImage": "/images/hero-3.png"
            },
            {
                "title": "Engineering Marvels,",
                "description": "Pushing the boundaries of what's possible through cutting-edge engineering and visionary design thinking.",
                "projectName": "Riverside Development - London",
                "backgroundImage": "/images/hero-4.png"
            }
        ],
        "Partners": [
            "/images/RiyadhLogo.svg",
            "/images/RiyadhLogo1.svg",
            "/images/RiyadhLogo.svg",
            "/images/RiyadhLogo1.svg",
            "/images/RiyadhLogo.svg",
            "/images/RiyadhLogo1.svg"
        ],
        "Stats": [
            {
                "value": 73,
                "title": "Fact Title",
                "description": "Lorem ipsum dolor sit amet consectetur. Iaculis nisi eu massa pretium eget.",
                "suffix": "+"
            },
            {
                "value": 100000,
                "title": "Fact Title",
                "description": "Lorem ipsum dolor sit amet consectetur. Iaculis nisi eu massa pretium eget.",
                "suffix": "+"
            },
            {
                "value": 12345687,
                "title": "Fact Title",
                "description": "Lorem ipsum dolor sit amet consectetur. Iaculis nisi eu massa pretium eget.",
                "suffix": "+"
            },
            {
                "value": 7,
                "title": "Fact Title",
                "description": "Lorem ipsum dolor sit amet consectetur. Iaculis nisi eu massa pretium eget.",
                "suffix": "+"
            }
        ],
        "Services": [
            {
                "imageUrl": "/images/sector1.svg",
                "title": "Road Construction",
                "alt": "Road construction services"
            },
            {
                "imageUrl": "/images/sector2.svg",
                "title": "Civil Engineering",
                "alt": "Civil engineering services"
            },
            {
                "imageUrl": "/images/sector1.svg",
                "title": "Infrastructure Development",
                "alt": "Infrastructure development services"
            },
            {
                "imageUrl": "/images/sector2.svg",
                "title": "Project Management",
                "alt": "Project management services"
            },
            {
                "imageUrl": "/images/sector1.svg",
                "title": "Consulting Services",
                "alt": "Consulting services"
            },
            {
                "imageUrl": "/images/sector2.svg",
                "title": "Maintenance",
                "alt": "Maintenance services"
            }
        ],
        "Latest": [
            {
                "imageUrl": "/images/sector1.svg",
                "title": "New Infrastructure Project",
                "date": "March 15, 2024",
                "description": "We're excited to announce our latest infrastructure development project in the heart of the city.",
                "alt": "Latest project image"
            },
            {
                "imageUrl": "/images/sector2.svg",
                "title": "Technology Integration",
                "date": "March 10, 2024",
                "description": "Implementing cutting-edge technology in our construction processes for better efficiency.",
                "alt": "Technology integration image"
            },
            {
                "imageUrl": "/images/sector2.svg",
                "title": "Community Development",
                "date": "March 5, 2024",
                "description": "Our commitment to sustainable community development continues with new initiatives.",
                "alt": "Community development image"
            }
        ],
        "ServiceSlider": [
            {
                "title": "Project Name",
                "description": "Lorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum etiam est consectetur is sitvulputate",
                "image": "/images/sliderImage.jpg"
            },
            {
                "title": "Project Name",
                "description": "Lorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum etiam est consectetur is sitvulputate",
                "image": "/images/sliderImage.jpg"
            },
            {
                "title": "Project Name",
                "description": "Lorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum etiam est consectetur is sitvulputate",
                "image": "/images/sliderImage.jpg"
            },
            {
                "title": "Project Name",
                "description": "Lorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum dolor sit amet coEgestas eu suscipit turpis duis etiam est consectetur is sitvulputateLorem ipsum etiam est consectetur is sitvulputate",
                "image": "/images/sliderImage.jpg"
            }
        ]
};
